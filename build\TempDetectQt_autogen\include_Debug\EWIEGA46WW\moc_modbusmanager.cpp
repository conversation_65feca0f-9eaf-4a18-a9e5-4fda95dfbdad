/****************************************************************************
** Meta object code from reading C++ file 'modbusmanager.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../modbusmanager.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#include <QtCore/QVector>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'modbusmanager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ModbusManager_t {
    QByteArrayData data[17];
    char stringdata0[230];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ModbusManager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ModbusManager_t qt_meta_stringdata_ModbusManager = {
    {
QT_MOC_LITERAL(0, 0, 13), // "ModbusManager"
QT_MOC_LITERAL(1, 14, 23), // "connectionStatusChanged"
QT_MOC_LITERAL(2, 38, 0), // ""
QT_MOC_LITERAL(3, 39, 9), // "connected"
QT_MOC_LITERAL(4, 49, 16), // "temperaturesRead"
QT_MOC_LITERAL(5, 66, 14), // "QVector<float>"
QT_MOC_LITERAL(6, 81, 12), // "temperatures"
QT_MOC_LITERAL(7, 94, 13), // "errorOccurred"
QT_MOC_LITERAL(8, 108, 5), // "error"
QT_MOC_LITERAL(9, 114, 15), // "deviceConnected"
QT_MOC_LITERAL(10, 130, 18), // "deviceDisconnected"
QT_MOC_LITERAL(11, 149, 13), // "connectDevice"
QT_MOC_LITERAL(12, 163, 12), // "SerialConfig"
QT_MOC_LITERAL(13, 176, 6), // "config"
QT_MOC_LITERAL(14, 183, 16), // "disconnectDevice"
QT_MOC_LITERAL(15, 200, 16), // "readTemperatures"
QT_MOC_LITERAL(16, 217, 12) // "slaveAddress"

    },
    "ModbusManager\0connectionStatusChanged\0"
    "\0connected\0temperaturesRead\0QVector<float>\0"
    "temperatures\0errorOccurred\0error\0"
    "deviceConnected\0deviceDisconnected\0"
    "connectDevice\0SerialConfig\0config\0"
    "disconnectDevice\0readTemperatures\0"
    "slaveAddress"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ModbusManager[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       8,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   54,    2, 0x06 /* Public */,
       4,    1,   57,    2, 0x06 /* Public */,
       7,    1,   60,    2, 0x06 /* Public */,
       9,    0,   63,    2, 0x06 /* Public */,
      10,    0,   64,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      11,    1,   65,    2, 0x0a /* Public */,
      14,    0,   68,    2, 0x0a /* Public */,
      15,    1,   69,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::Bool,    3,
    QMetaType::Void, 0x80000000 | 5,    6,
    QMetaType::Void, QMetaType::QString,    8,
    QMetaType::Void,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Bool, 0x80000000 | 12,   13,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   16,

       0        // eod
};

void ModbusManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ModbusManager *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->connectionStatusChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 1: _t->temperaturesRead((*reinterpret_cast< const QVector<float>(*)>(_a[1]))); break;
        case 2: _t->errorOccurred((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->deviceConnected(); break;
        case 4: _t->deviceDisconnected(); break;
        case 5: { bool _r = _t->connectDevice((*reinterpret_cast< const SerialConfig(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 6: _t->disconnectDevice(); break;
        case 7: _t->readTemperatures((*reinterpret_cast< int(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 1:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QVector<float> >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ModbusManager::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ModbusManager::connectionStatusChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ModbusManager::*)(const QVector<float> & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ModbusManager::temperaturesRead)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ModbusManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ModbusManager::errorOccurred)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ModbusManager::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ModbusManager::deviceConnected)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ModbusManager::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ModbusManager::deviceDisconnected)) {
                *result = 4;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ModbusManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_ModbusManager.data,
    qt_meta_data_ModbusManager,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ModbusManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ModbusManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ModbusManager.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ModbusManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    }
    return _id;
}

// SIGNAL 0
void ModbusManager::connectionStatusChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ModbusManager::temperaturesRead(const QVector<float> & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void ModbusManager::errorOccurred(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void ModbusManager::deviceConnected()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ModbusManager::deviceDisconnected()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
