﻿#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QModbusRtuSerialMaster>
#include <QTimer>
#include <QVector>
#include <QSerialPort>
#include "qcustomplot.h" // 如果使用图表功能需要添加

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class ModbusManager;
class TemperatureData;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    // 串口控制
    void onOpenPortClicked();
    void onReadDataClicked();
    void onSaveDataClicked();
    void onTimerStateChanged(int state);
    
    // 数据显示更新
    void updateTemperatureDisplay(const QVector<float>& temperatures);
    void updateConnectionStatus(bool connected);
    void showStatusMessage(const QString& message, int timeout = 0);

    // 图表相关（如果需要）
    void updatePlot();  // 改回不带参数的版本

    void on_btn_resettime_clicked();  // 自动连接的槽函数

    void onDeviceConnected();      // 设备连接时的槽函数
    void onDeviceDisconnected();   // 设备断开时的槽函数

    //void onResetButtonClicked();  // 重置按钮的点击事件处理函数

private:
    // 初始化函数
    void initializeUi();
    void setupConnections();
    void initializePlot();  // 如果使用图表功能
    void initializeModbusManager();
    void updateTimeDisplay();


private:
    Ui::MainWindow* ui;
    ModbusManager* m_modbusManager;
    TemperatureData* m_temperatureData;
    QTimer m_timer;

    // 图表相关变量（如果需要）
    QVector<QCPAxisRect*> m_subPlots;  // 存储子图指针
    QVector<double> m_timeData;         // 存储时间数据
    QVector<QCPGraph*> m_manualMarkers;
    QVector<float> m_lastTemperatures;  // 添加这个成员变量来存储最新的温度数据

    // 测试模式相关
#if MODBUS_TEST_MODE
    void logTestModeStatus();
#endif

    QVector<QCheckBox*> m_tempCheckBoxes;  // 存储温度复选框的引用

    QTime m_samplingStartTime;  // 记录采样开始时间
    int m_totalSamples = 0;      // 记录总采样点数
    int m_samplingInterval; // 采样间隔，单位为毫秒
    int m_accumulatedSeconds = 0; // Total accumulated seconds across all sampling sessions
    bool m_isTimerRunning = false;// Track if timer is currently running

    QStatusBar* m_statusBar;        // 状态栏
    QLabel* m_statusMessageLabel;   // 状态消息标签
    QLabel* m_realTimeLabel;        // 实时时间标签
    QLabel* m_totalTimeLabel;       // 运行总时长标签
    QDateTime m_startTime;          // 软件启动时间
    QTimer m_timeDisplayTimer;
};

#endif // MAINWINDOW_H
