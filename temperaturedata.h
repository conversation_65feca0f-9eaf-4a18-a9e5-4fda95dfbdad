﻿#ifndef TEMPERATUREDATA_H
#define TEMPERATUREDATA_H

#include <QObject>
#include <QVector>
#include <QDateTime>
#include <QFile>
#include <QTextStream>
#include <QDir>
#include <QTime>
#include <QList>
#include <QCryptographicHash>
#include <QTimer>

class TemperatureData : public QObject
{
    Q_OBJECT

public:
    explicit TemperatureData(QObject *parent = nullptr);

    struct TemperatureRecord {
        QDateTime timestamp;
        QVector<float> temperatures;
    };

    struct DataPoint {
        double value;
        QDateTime timestamp;
        bool isManual;  // 标记是否为手动记录
        QString checksum;  // 数据校验和
    };
    // 动态阈值配置
    struct SaveThreshold {
        int timeInterval;     // 时间间隔（秒）
        int dataPointCount;   // 数据点数量
        qint64 maxFileSize;   // 单个文件最大大小
        int maxFileCount;     // 最大文件数量
    };

    // 采样间隔变更记录
    struct SamplingIntervalChange {
        QDateTime changeTime;
        int oldInterval;
        int newInterval; 
        QString reason;
        
        SamplingIntervalChange() = default;
        SamplingIntervalChange(QDateTime time, int oldInt, int newInt, const QString& r)
            : changeTime(time), oldInterval(oldInt), newInterval(newInt), reason(r) {}
    };

    // 采集会话（轮次）管理
    struct AcquisitionSession {
        QString sessionId;
        QDateTime startTime;
        QDateTime endTime;
        QVector<SamplingIntervalChange> intervalHistory;
        int totalSamples = 0;
        bool isActive = false;
        
        AcquisitionSession() = default;
        AcquisitionSession(const QString& id) 
            : sessionId(id), startTime(QDateTime::currentDateTime()), isActive(true) {}
            
        // 获取会话持续时间（秒）
        int getDurationSeconds() const {
            if (!endTime.isValid() && isActive) {
                return startTime.secsTo(QDateTime::currentDateTime());
            }
            return startTime.secsTo(endTime);
        }
        
        // 获取当前有效的采样间隔
        int getCurrentInterval() const {
            return intervalHistory.isEmpty() ? 1000 : intervalHistory.last().newInterval;
        }
    };

public slots:
    void addTemperatureData(const QVector<float>& temperatures, bool isManual = false);
    bool saveToCSV(const QString& fileName);
    bool saveSegmentToCSV(const QString& fileName);
    void clearData();
    void setSamplingRate(int ms) { m_samplingRate = ms; }
    void setMaxDataPoints(int points) { m_maxDataPoints = points; }
    void autoSaveSegment();
    void setSamplingInterval(int interval) { m_samplingInterval = interval; }
    void setTotalSamplingTime(int totalTime) { m_totalSamplingTime = totalTime; }
    void setSaveThreshold(const SaveThreshold& threshold) { m_saveThreshold = threshold; }
    void startAutoBackup() { m_backupTimer->start(); }
    void stopAutoBackup() { m_backupTimer->stop(); }
    bool restoreFromBackup(const QString& backupFile);
    bool verifyDataIntegrity();

    // 会话管理方法
    QString startNewSession(const QString& sessionName = QString());
    bool endCurrentSession();
    bool changeSamplingInterval(int newInterval, const QString& reason = "User modified");
    const AcquisitionSession* getCurrentSession() const;
    QVector<AcquisitionSession> getSessionHistory() const;
    int getActiveSessionCount() const;
    void clearSessionHistory();
    
    // 获取会话统计信息
    QString getCurrentSessionInfo() const;
    QString getSessionSummary(const QString& sessionId) const;

    const QVector<DataPoint>& getChannelData(int channel) const {
        static QVector<DataPoint> empty;
        return channel < m_channelData.size() ? m_channelData[channel] : empty;
    }

    QList<double> data() const;  // 返回数据

signals:
    void dataSaved(bool success);
    void backupCompleted(bool success);
    void dataIntegrityError(const QString& error);
    void systemStatusUpdate(const QString& status);
    
    // 会话管理信号
    void sessionStarted(const QString& sessionId);
    void sessionEnded(const QString& sessionId, int durationSeconds);
    void samplingIntervalChanged(int oldInterval, int newInterval, const QString& reason);
    void sessionStatsUpdated(const QString& sessionId, int totalSamples);

private:
    static const int MAX_RECORDS = 1000;                    // 最大记录数
    const QString DATA_DIR_NAME = "temperature_data";       // 数据目录名
    const QString BACKUP_DIR_NAME = "backup_data";         // 备份目录名
    
    QVector<TemperatureRecord> m_records;
    int m_samplingRate;                                     // 采样率（毫秒）
    QString m_dataPath;                                     // 数据路径
    QString m_backupPath;                                   // 备份路径
    
    QVector<QVector<DataPoint>> m_channelData;
    int m_maxDataPoints = 3600;  // 默认存储1小时的数据
    
    SaveThreshold m_saveThreshold;  // 动态保存阈值
    int m_dataCountSinceLastSave = 0;              // 上次保存后的新数据计数
    QDateTime m_lastSaveTime;                      // 上次保存时间
    
    QTime m_samplingStartTime;  // 记录采样开始时间
    int m_samplingInterval = 1000;      // 采样间隔，单位为毫秒
    int m_totalSamplingTime = 0;    // 总采样时间（秒）

    QTimer* m_backupTimer;         // 自动备份定时器
    QList<double> m_data;          // 温度数据容器
    
    // 会话管理相关成员变量
    QVector<AcquisitionSession> m_sessionHistory;    // 会话历史记录
    AcquisitionSession* m_currentSession = nullptr;  // 当前活跃会话
    int m_sessionCounter = 0;                        // 会话计数器，用于生成唯一ID
    
    QString generateFileName() const;  // 生成文件名
    QString calculateChecksum(const DataPoint& point) const;  // 计算数据校验和
    bool createBackup();           // 创建备份
    void cleanupOldFiles();        // 清理旧文件
    void logSystemStatus(const QString& status);  // 记录系统状态
    bool validateDataPoint(const DataPoint& point) const;  // 验证数据点完整性
};

#endif // TEMPERATUREDATA_H
