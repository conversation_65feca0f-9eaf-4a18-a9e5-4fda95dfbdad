#include "mainwindow.h"
#include "configurationmanager.h"

#include <QApplication>
#include <QDebug>

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);
    
    // 设置应用程序信息，用于配置文件生成
    QCoreApplication::setApplicationName("TempDetectQt");
    QCoreApplication::setApplicationVersion("1.0.0");
    QCoreApplication::setOrganizationName("TempMonitor");
    
    // 初始化配置管理器
    ConfigurationManager* configManager = ConfigurationManager::instance();
    
    // 尝试加载配置文件，如果不存在则创建默认配置
    if (!configManager->loadFromFile()) {
        qWarning() << "Failed to load configuration, using defaults";
    } else {
        qDebug() << "Configuration loaded successfully";
        qDebug() << "Config path:" << configManager->getConfigPath();
        qDebug() << "Serial port:" << configManager->serialPortConfig().portName;
        qDebug() << "Baud rate:" << configManager->serialPortConfig().baudRate;
        qDebug() << "Sampling interval:" << configManager->dataAcquisitionConfig().samplingIntervalMs << "ms";
    }

    MainWindow w;
    w.show();

    return a.exec();
}
