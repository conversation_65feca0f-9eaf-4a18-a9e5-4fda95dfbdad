#include "mainwindow.h"
#include "ui_mainwindow.h"
#include "modbusmanager.h"
#include "temperaturedata.h"
#include <QMessageBox>
#include <QFileDialog>
#include <QDebug>

QVector<float>savewd = QVector<float>();
QVector<float>savewd2 = QVector<float>();
QVector<float>savewd3 = QVector<float>();
QVector<float>savewd4 = QVector<float>();
QVector<float>savewd5 = QVector<float>();
QVector<float>savewd6 = QVector<float>();
QVector<float>savewd7 = QVector<float>();
QVector<float>savewd8 = QVector<float>();
QVector<float>savewd9 = QVector<float>();
QVector<float>savewd10 = QVector<float>();
QVector<float>x = QVector<float>();
QVector<float>y = QVector<float>();

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , m_modbusManager(new ModbusManager(this))
    , m_temperatureData(new TemperatureData(this))
{
    ui->setupUi(this);

    // 获取主窗口的状态栏
    m_statusBar = statusBar();

    // 添加状态消息标签（左侧）
    m_statusMessageLabel = new QLabel(this);
    m_statusBar->addWidget(m_statusMessageLabel);  // 默认靠左显示

    // 添加实时时间标签（靠右）
    m_realTimeLabel = new QLabel(this);
    m_statusBar->addPermanentWidget(m_realTimeLabel);  // 永久部件，靠右显示

    // 添加运行总时长标签（靠右）
    m_totalTimeLabel = new QLabel(this);
    m_statusBar->addPermanentWidget(m_totalTimeLabel);  // 永久部件，靠右显示

    // 初始化运行总时长计时器
    m_startTime = QDateTime::currentDateTime();
    connect(&m_timeDisplayTimer, &QTimer::timeout, this, &MainWindow::updateTimeDisplay);
    m_timeDisplayTimer.start(1000);  // 每秒更新一次

    // 初始化时间显示
    updateTimeDisplay();

    // 设置窗口标题
    QString windowTitle = tr("温度监控系统 by: Minhui Ye");
#if MODBUS_TEST_MODE
    windowTitle += " - " + tr("测试模式");
#endif
    this->setWindowTitle(windowTitle);

    QIcon logo("logo.ico");
    this->setWindowIcon(logo);

    initializeUi();
    setupConnections();

    connect(m_modbusManager, &ModbusManager::deviceConnected, this, &MainWindow::onDeviceConnected);
    connect(m_modbusManager, &ModbusManager::deviceDisconnected, this, &MainWindow::onDeviceDisconnected);

#if MODBUS_TEST_MODE

#else
    ui->checkBox_2->setChecked(false); // 取消勾选
    ui->checkBox_2->setEnabled(false); // 禁用定时选框
#endif

    // 连接重置按钮的点击信号
    connect(ui->btn_resettime, &QPushButton::clicked, this, &MainWindow::on_btn_resettime_clicked);
}

MainWindow::~MainWindow()
{
    if (m_modbusManager) {
        m_modbusManager->disconnectDevice();
    }
    delete ui;
}

void MainWindow::initializeUi()
{
    // 串口设置初始化
    for (int i = 1; i <= 10; i++) {
        ui->portName->addItem(QString("COM%1").arg(i));
    }

    // 波特率设置
    ui->buadRate->addItem("9600", QSerialPort::Baud9600);
    ui->buadRate->addItem("19200", QSerialPort::Baud19200);
    ui->buadRate->addItem("38400", QSerialPort::Baud38400);
    ui->buadRate->addItem("57600", QSerialPort::Baud57600);
    ui->buadRate->addItem("115200", QSerialPort::Baud115200);

    // 数据位设置
    ui->dataBits->addItem("8", QSerialPort::Data8);
    ui->dataBits->addItem("7", QSerialPort::Data7);
    ui->dataBits->addItem("6", QSerialPort::Data6);
    ui->dataBits->addItem("5", QSerialPort::Data5);

    // 校验位设置
    ui->parity->addItem(tr("无"), QSerialPort::NoParity);
    ui->parity->addItem(tr("奇"), QSerialPort::OddParity);
    ui->parity->addItem(tr("偶"), QSerialPort::EvenParity);

    // 停止位设置
    ui->stopBits->addItem("1", QSerialPort::OneStop);
    ui->stopBits->addItem("1.5", QSerialPort::OneAndHalfStop);
    ui->stopBits->addItem("2", QSerialPort::TwoStop);

    // 默认从站地址
    ui->sAddr1->setText("1");
    
    // 默认定时器间隔
    ui->timelineEdit_2->setText("1000");

    // 设置数据点数量限制（根据采样间隔调整）
    int samplingInterval = ui->timelineEdit_2->text().toInt();  // 毫秒
    int maxPoints = (3600 * 1000) / samplingInterval;  // 存储1小时的数据
    m_temperatureData->setMaxDataPoints(maxPoints);

    // 初始化图表设置
    initializePlot();

    // 初始化温度复选框
    m_tempCheckBoxes = {
        ui->cb_tmp1, ui->cb_tmp2, ui->cb_tmp3, ui->cb_tmp4, ui->cb_tmp5,
        ui->cb_tmp6, ui->cb_tmp7, ui->cb_tmp8, ui->cb_tmp9, ui->cb_tmp10
    };

    // 设置所有复选框默认选中
    for (auto checkbox : m_tempCheckBoxes) {
        checkbox->setChecked(true);
    }

    // Initialize sampling interval from the UI
    m_samplingInterval = ui->timelineEdit_2->text().toInt();

    // Initialize the total time display
    ui->le_totalTime->setText("0 s");
}

void MainWindow::initializePlot()
{
    // 启用OpenGL加速
    ui->PlotCurve->setOpenGl(true);
    qDebug()<<"opengle="<<ui->PlotCurve->openGl();
    
    // 创建一个5x2的网格布局
    ui->PlotCurve->plotLayout()->clear();
    QCPMarginGroup* marginGroup = new QCPMarginGroup(ui->PlotCurve);
    
    m_subPlots.clear();
    
    // 创建10个子图，5行2列排布
    for (int i = 0; i < 10; ++i) {
        QCPAxisRect* axisRect = new QCPAxisRect(ui->PlotCurve);
        m_subPlots.append(axisRect);
        
        // 计算行列位置
        int row = i % 5;    // 0,5在第一行，1,6在第二行，以此类推
        int col = i / 5;    // 0-4在第一列，5-9在第二列
        
        ui->PlotCurve->plotLayout()->addElement(row, col, axisRect);
        axisRect->setMarginGroup(QCP::msLeft | QCP::msRight, marginGroup);
        
        QCPGraph* graph = ui->PlotCurve->addGraph(axisRect->axis(QCPAxis::atBottom), 
                                                 axisRect->axis(QCPAxis::atLeft));
        
        // 优化大数据量显示
        graph->setAdaptiveSampling(true);  // 启用自适应采样
        graph->setLineStyle(QCPGraph::lsLine);  // 使用直线而不是曲线
        graph->setAntialiased(false);  // 关闭抗锯齿以提高性能
        
        graph->setPen(QPen(QColor::fromHsv(i * 36, 255, 255), 1));  // 减小线宽
        
        axisRect->axis(QCPAxis::atLeft)->setLabel(QString("温度%1(℃)").arg(i + 1));
        
        // 只在最底部的图表显示X轴标签
        if (row == 4) {  // 最后一行的图表
            axisRect->axis(QCPAxis::atBottom)->setLabel(tr("时间(s)"));
        }
        
        // 设置初始范围
        axisRect->axis(QCPAxis::atBottom)->setRange(0, 100);
        axisRect->axis(QCPAxis::atLeft)->setRange(-200, 50);
        
        // 启用交互
        axisRect->setRangeZoom(Qt::Horizontal | Qt::Vertical);
        axisRect->setRangeDrag(Qt::Horizontal | Qt::Vertical);
    }
    
    // 设置行间距
    ui->PlotCurve->plotLayout()->setRowSpacing(0);
}

void MainWindow::setupConnections()
{
    // 连接ModbusManager的信号
    connect(m_modbusManager, &ModbusManager::connectionStatusChanged,
            this, &MainWindow::updateConnectionStatus);
    connect(m_modbusManager, &ModbusManager::temperaturesRead,
            this, &MainWindow::updateTemperatureDisplay);
    connect(m_modbusManager, &ModbusManager::errorOccurred,
            this, [this](const QString& error) { showStatusMessage(error); });

    // 定时器连接
    connect(&m_timer, &QTimer::timeout,
            this, &MainWindow::onReadDataClicked);

    // 按钮连接
    connect(ui->openButton, &QPushButton::clicked,
            this, &MainWindow::onOpenPortClicked);
    connect(ui->mReadBtn, &QPushButton::clicked,
            this, &MainWindow::onReadDataClicked);
    connect(ui->saveButton, &QPushButton::clicked,
            this, &MainWindow::onSaveDataClicked);
    connect(ui->checkBox_2, &QCheckBox::stateChanged,
            this, &MainWindow::onTimerStateChanged);
}

void MainWindow::onOpenPortClicked()
{
    if (ui->openButton->text() == tr("打开串口")) {
        ModbusManager::SerialConfig config;
        config.portName = ui->portName->currentText();
        config.baudRate = ui->buadRate->currentData().toInt();
        config.dataBits = static_cast<QSerialPort::DataBits>(ui->dataBits->currentData().toInt());
        config.parity = static_cast<QSerialPort::Parity>(ui->parity->currentData().toInt());
        config.stopBits = static_cast<QSerialPort::StopBits>(ui->stopBits->currentData().toInt());
        config.flowControl = QSerialPort::NoFlowControl;

        if (m_modbusManager->connectDevice(config)) {
            ui->openButton->setText(tr("断开串口"));
            ui->groupBox->setEnabled(false);
        }
    } else {
        m_modbusManager->disconnectDevice();
        ui->openButton->setText(tr("打开串口"));
        ui->groupBox->setEnabled(true);
    }
}

void MainWindow::onReadDataClicked()
{
    if (m_modbusManager) {
        int startRegister = ui->startRegSpinBox->value();
        m_modbusManager->readTemperatures(ui->sAddr1->text().toInt(), startRegister);
        // 手动读取时添加标记
        if (!ui->checkBox_2->isChecked()) {  // 如果不是定时器触发的
            m_temperatureData->addTemperatureData(m_lastTemperatures, true);
        }
    }
}

void MainWindow::updateTemperatureDisplay(const QVector<float>& temperatures)
{
    if (temperatures.size() >= 10) {
        // 更新显示，只更新选中的通道
        QVector<float> filteredTemps;
        filteredTemps.reserve(temperatures.size());

        for (int i = 0; i < temperatures.size(); ++i) {
            if (m_tempCheckBoxes[i]->isChecked()) {
                filteredTemps.append(temperatures[i]);
                // 更新对应的显示
                switch (i) {
                    case 0: ui->nowwd->setText(QString::number(temperatures[i], 'f', 1)); break;
                    case 1: ui->nowwd_2->setText(QString::number(temperatures[i], 'f', 1)); break;
                    case 2: ui->nowwd_3->setText(QString::number(temperatures[i], 'f', 1)); break;
                    case 3: ui->nowwd_4->setText(QString::number(temperatures[i], 'f', 1)); break;
                    case 4: ui->nowwd_5->setText(QString::number(temperatures[i], 'f', 1)); break;
                    case 5: ui->nowwd_6->setText(QString::number(temperatures[i], 'f', 1)); break;
                    case 6: ui->nowwd_7->setText(QString::number(temperatures[i], 'f', 1)); break;
                    case 7: ui->nowwd_8->setText(QString::number(temperatures[i], 'f', 1)); break;
                    case 8: ui->nowwd_9->setText(QString::number(temperatures[i], 'f', 1)); break;
                    case 9: ui->nowwd_10->setText(QString::number(temperatures[i], 'f', 1)); break;
                }
            } else {
                filteredTemps.append(-999999); // 使用特殊值标记未选中的通道
            }
        }

        m_lastTemperatures = filteredTemps;

        // 只有在定时器模式下才更新图表
        if (ui->checkBox_2->isChecked()) {
            m_temperatureData->addTemperatureData(filteredTemps, false);
            updatePlot();
        }
    }
}

void MainWindow::updatePlot()
{
    const double DISPLAY_WINDOW = 30.0;  // 显示窗口改为30秒

    for (int i = 0; i < 10; ++i) {
        // 如果通道未选中，隐藏对应的图表
        if (!m_tempCheckBoxes[i]->isChecked()) {
            ui->PlotCurve->graph(i)->setVisible(false);
            continue;
        }

        ui->PlotCurve->graph(i)->setVisible(true);
        const QVector<TemperatureData::DataPoint>& channelData = m_temperatureData->getChannelData(i);
        if (channelData.isEmpty()) continue;

        QVector<double> times;
        QVector<double> values;
        times.reserve(channelData.size());
        values.reserve(channelData.size());
        
        // 获取最新时间点和起始时间点
        QDateTime latestTime = channelData.last().timestamp;
        QDateTime firstTime = channelData.first().timestamp;
        double totalSeconds = firstTime.msecsTo(latestTime) / 1000.0;
        
        // 确定窗口起始时间
        QDateTime windowStart;
        bool isScrolling = totalSeconds > DISPLAY_WINDOW;
        
        if (isScrolling) {
            // 如果总时间超过30秒，使用滚动窗口
            windowStart = latestTime.addSecs(-DISPLAY_WINDOW);
        } else {
            // 如果不到30秒，从开始时间显示
            windowStart = firstTime;
        }

        // 找到有效的最大最小值
        double minY = std::numeric_limits<double>::max();
        double maxY = std::numeric_limits<double>::lowest();
        bool hasValidData = false;

        // 处理数据点
        for (const auto& point : channelData) {
            if (point.timestamp >= windowStart) {
                if (point.value > -999990) {  // 检查是否是有效数据
                    double seconds;
                    if (isScrolling) {
                        // 在滚动模式下，时间从0开始
                        seconds = windowStart.msecsTo(point.timestamp) / 1000.0;
                    } else {
                        // 在累积模式下，显示实际经过的时间
                        seconds = firstTime.msecsTo(point.timestamp) / 1000.0;
                    }
                    
                    times.append(seconds);
                    values.append(point.value);
                    
                    // 更新最大最小值
                    minY = std::min(minY, point.value);
                    maxY = std::max(maxY, point.value);
                    hasValidData = true;
                }
            }
        }

        // 更新图表数据
        QCPGraph* graph = ui->PlotCurve->graph(i);
        graph->setData(times, values);

        // 只有在有有效数据时才更新Y轴范围
        if (hasValidData) {
            // 添加一定的边距，使显示更美观
            double margin = (maxY - minY) * 0.1;  // 10%的边距
            if (margin < 0.1) margin = 0.1;  // 确保至少有一些边距

            // 获取当前Y轴范围
            QCPRange currentRange = m_subPlots[i]->axis(QCPAxis::atLeft)->range();
            double newMin = minY - margin;
            double newMax = maxY + margin;

            // 如果新范围与当前范围差异显著，则更新范围
            if (currentRange.size() == 0 || 
                std::abs(currentRange.lower - newMin) > margin || 
                std::abs(currentRange.upper - newMax) > margin) {
                m_subPlots[i]->axis(QCPAxis::atLeft)->setRange(newMin, newMax);
            }
        }

        // 设置X轴范围
        if (!times.isEmpty()) {
            if (isScrolling) {
                // 固定30秒窗口
                m_subPlots[i]->axis(QCPAxis::atBottom)->setRange(0, DISPLAY_WINDOW);
            } else {
                // 动态扩展直到达到30秒
                double xMax = std::min(totalSeconds, DISPLAY_WINDOW);
                m_subPlots[i]->axis(QCPAxis::atBottom)->setRange(0, xMax);
            }
        }
    }

    // 更新总时间显示
    if (m_samplingStartTime.isValid()) {
        int elapsedSeconds = m_samplingStartTime.secsTo(QTime::currentTime());
        ui->le_totalTime->setText(QString("%1 s").arg(elapsedSeconds));  // 更新总时间显示
    }

    // 使用定时器延迟重绘，避免过于频繁
    static QTimer replotTimer;
    replotTimer.setSingleShot(true);
    if (!replotTimer.isActive()) {
        replotTimer.start(16);  // 约60fps
        connect(&replotTimer, &QTimer::timeout, ui->PlotCurve, [this]() {
            ui->PlotCurve->replot(QCustomPlot::rpQueuedReplot);
        });
    }
}

void MainWindow::onTimerStateChanged(int state)
{
    if (state == Qt::Checked) {
#if MODBUS_TEST_MODE
        // 测试模式下跳过设备连接检查
        qDebug() << "Test Mode: Timer started without device connection check";
#else
        // 正常模式下检查设备是否已连接
        if (!m_modbusManager || m_modbusManager->deviceState() != QModbusDevice::ConnectedState) {
            showStatusMessage(tr("设备未连接，请先连接设备。"));
            ui->checkBox_2->setChecked(false);  // 取消勾选
            return;  // 退出函数，避免崩溃
        }
#endif

        bool ok;
        int interval = ui->timelineEdit_2->text().toInt(&ok);
        if (ok && interval > 0) {
            m_timer.start(interval);
            m_temperatureData->setSamplingRate(interval);  // 更新采样率
            m_samplingStartTime = QTime::currentTime();    // 初始化采样开始时间
            m_accumulatedSeconds = 0;                     // 重置累计时间
            m_isTimerRunning = true;                      // 标记定时器为运行状态
            showStatusMessage(tr("定时采集已启动"));
        } else {
            showStatusMessage(tr("无效的定时间隔"));
            ui->checkBox_2->setChecked(false);
        }
    } else {
        m_timer.stop();
        m_isTimerRunning = false;                         // 标记定时器为停止状态
        showStatusMessage(tr("定时采集已停止"));
    }
}

void MainWindow::onSaveDataClicked()
{
    QString fileName = QFileDialog::getSaveFileName(
        this,
        tr("保存数据"),
        QDir::homePath(),
        tr("CSV文件 (*.csv)")
    );

    if (!fileName.isEmpty()) {
        // Update total sampling time before saving
        int currentSessionSeconds = m_isTimerRunning ? m_samplingStartTime.secsTo(QTime::currentTime()) : 0;
        int totalSeconds = m_accumulatedSeconds + currentSessionSeconds;
        m_temperatureData->setTotalSamplingTime(totalSeconds);
        m_temperatureData->setSamplingInterval(m_samplingInterval);

        if (m_temperatureData->saveToCSV(fileName)) {
            showStatusMessage(tr("数据保存成功"));
            m_temperatureData->clearData();
        } else {
            showStatusMessage(tr("数据保存失败"));
        }
    }
}

void MainWindow::updateConnectionStatus(bool connected)
{
    if (connected) {
        showStatusMessage(tr("连接成功"));
    } else {
        showStatusMessage(tr("连接断开"));
    }
}

void MainWindow::showStatusMessage(const QString& message, int timeout)
{
    ui->statusBar->showMessage(message, timeout);
}

void MainWindow::on_btn_resettime_clicked()
{
    // 检查数据是否有效
    if (m_temperatureData->data().isEmpty()) {  // 假设数据存储在 QList 或 QVector 中
        showStatusMessage(tr("没有可重置的数据。"));
        return;  // 数据为空，直接返回
    }

    // 执行重置操作
    m_temperatureData->clearData();  // 使用正确的方法名称
    m_accumulatedSeconds = 0;
    m_samplingStartTime = QTime::currentTime();
    ui->le_totalTime->setText("0 s");
    showStatusMessage(tr("数据已重置。"));
}

void MainWindow::onDeviceConnected()
{
    // 设备连接成功后的处理逻辑
    showStatusMessage(tr("设备已连接"));
    ui->checkBox_2->setEnabled(true);
}

void MainWindow::onDeviceDisconnected()
{
    // 设备断开后的处理逻辑
    showStatusMessage(tr("设备已断开"));
    ui->checkBox_2->setChecked(false); // 取消勾选
    ui->checkBox_2->setEnabled(false); // 禁用定时选框
}

void MainWindow::initializeModbusManager()
{
    m_modbusManager = new ModbusManager(this);
#if MODBUS_TEST_MODE
    // 测试模式下模拟设备连接
    emit m_modbusManager->connectionStatusChanged(true);
#else
    // 正常模式下连接设备
    connect(m_modbusManager, &ModbusManager::connectionStatusChanged, this, &MainWindow::updateConnectionStatus);
#endif
}

void MainWindow::updateTimeDisplay()
{
    // 更新实时时间
    QDateTime currentTime = QDateTime::currentDateTime();
    m_realTimeLabel->setText(tr("实时时间: %1").arg(currentTime.toString("yyyy-MM-dd hh:mm:ss")));

    // 计算软件运行总时长
    qint64 elapsedSeconds = m_startTime.secsTo(currentTime);
    int hours = elapsedSeconds / 3600;
    int minutes = (elapsedSeconds % 3600) / 60;
    int seconds = elapsedSeconds % 60;
    m_totalTimeLabel->setText(tr("运行总时长: %1:%2:%3")
                              .arg(hours, 2, 10, QLatin1Char('0'))
                              .arg(minutes, 2, 10, QLatin1Char('0'))
                              .arg(seconds, 2, 10, QLatin1Char('0')));
}



