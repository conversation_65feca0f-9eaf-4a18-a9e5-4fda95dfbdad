﻿#include "modbusmanager.h"
#include <QRandomGenerator>
#include <QDebug>

#if MODBUS_TEST_MODE
class ModbusSimulator {
public:
    static QVector<quint16> generateTestData() {
        QVector<quint16> testData;
        // 生成10个模拟温度值（范围：-20.0°C 到 50.0°C）
        // 在Modbus中，温度值放大10倍存储
        for (int i = 0; i < 10; ++i) {
            // 生成-200到500之间的随机数（对应-20.0°C到50.0°C）
            int temp = QRandomGenerator::global()->bounded(-200, 501);
            // 转换为Modbus格式（负温度需要转换为65535-|temp|）
            quint16 modbusValue = (temp < 0) ? (65535 + temp) : temp;
            testData.append(modbusValue);
        }
        return testData;
    }

    static void logModbusOperation(const QString& operation, const QVector<quint16>& data) {
        qDebug() << "===== Modbus Test Mode =====";
        qDebug() << "Operation:" << operation;
        qDebug() << "Raw Register Values:";
        for (int i = 0; i < data.size(); ++i) {
            float temp = convertRegisterToTemperature(data[i]);
            qDebug() << QString("Register %1: %2 (Temperature: %3°C)")
                        .arg(i)
                        .arg(data[i])
                        .arg(temp, 0, 'f', 1);
        }
        qDebug() << "=========================";
    }

    static float convertRegisterToTemperature(quint16 registerValue) {
        if (registerValue > 65535/2) {
            return (registerValue - 65535) * 0.1f;
        }
        return registerValue * 0.1f;
    }
};
#endif

ModbusManager::ModbusManager(QObject *parent)
    : QObject(parent)
    , m_modbusDevice(new QModbusRtuSerialMaster(this))
{
    // 设置基本参数
    m_modbusDevice->setTimeout(TIMEOUT_MS);
    m_modbusDevice->setNumberOfRetries(RETRY_COUNT);
}

ModbusManager::~ModbusManager()
{
    if (m_modbusDevice) {
        if (m_modbusDevice->state() == QModbusDevice::ConnectedState) {
            disconnectDevice();
        }
        delete m_modbusDevice;
    }
}

bool ModbusManager::connectDevice(const SerialConfig& config)
{
#if MODBUS_TEST_MODE
    qDebug() << "Test Mode: Connecting to" << config.portName
             << "with baud rate" << config.baudRate;
    emit connectionStatusChanged(true);
    return true;
#else
    if (m_modbusDevice->state() == QModbusDevice::ConnectedState) {
        disconnectDevice();
    }

    m_modbusDevice->setConnectionParameter(QModbusDevice::SerialPortNameParameter, config.portName);
    m_modbusDevice->setConnectionParameter(QModbusDevice::SerialBaudRateParameter, config.baudRate);
    m_modbusDevice->setConnectionParameter(QModbusDevice::SerialDataBitsParameter, config.dataBits);
    m_modbusDevice->setConnectionParameter(QModbusDevice::SerialParityParameter, config.parity);
    m_modbusDevice->setConnectionParameter(QModbusDevice::SerialStopBitsParameter, config.stopBits);

    if (m_modbusDevice->connectDevice()) {
        emit deviceConnected();
        emit connectionStatusChanged(true);
        return true;
    } else {
        emit deviceDisconnected();
        emit errorOccurred(m_modbusDevice->errorString());
        return false;
    }
#endif
}

void ModbusManager::readTemperatures(int slaveAddress, int startRegister)
{
#if MODBUS_TEST_MODE
    // 生成测试数据
    QVector<quint16> testRegisters = ModbusSimulator::generateTestData();
    ModbusSimulator::logModbusOperation("Read Temperatures", testRegisters);

    // 转换为温度值
    QVector<float> temperatures;
    for (quint16 value : testRegisters) {
        temperatures.append(ModbusSimulator::convertRegisterToTemperature(value));
    }
    
    // 发送数据
    emit temperaturesRead(temperatures);
#else
    if (!m_modbusDevice || m_modbusDevice->state() != QModbusDevice::ConnectedState) {
        emit errorOccurred(tr("设备未连接"));
        return;
    }

    QModbusDataUnit request(QModbusDataUnit::HoldingRegisters,
                           startRegister,
                           REGISTER_COUNT);

    if (QModbusReply* reply = m_modbusDevice->sendReadRequest(request, slaveAddress)) {
        if (!reply->isFinished()) {
            connect(reply, &QModbusReply::finished, this, [this, reply]() {
                handleModbusResponse(reply);
                reply->deleteLater();
            });
        } else {
            handleModbusResponse(reply);
            reply->deleteLater();
        }
    } else {
        emit errorOccurred(m_modbusDevice->errorString());
    }
#endif
}

void ModbusManager::handleModbusResponse(QModbusReply* reply)
{
    if (reply->error() == QModbusDevice::NoError) {
        const QModbusDataUnit unit = reply->result();
        QVector<float> temperatures;
        
        for (uint i = 0; i < unit.valueCount(); i++) {
            float temp = convertRegisterToTemperature(unit.value(i));
            temperatures.append(temp);
        }
        
        emit temperaturesRead(temperatures);
    } else {
        emit errorOccurred(reply->errorString());
    }
}

float ModbusManager::convertRegisterToTemperature(quint16 registerValue)
{
    if (registerValue > 65535/2) {
        return (registerValue - 65535) * 0.1f;
    }
    return registerValue * 0.1f;
}

void ModbusManager::disconnectDevice()
{
#if MODBUS_TEST_MODE
    qDebug() << "Test Mode: Disconnecting device";
    emit connectionStatusChanged(false);
#else
    if (m_modbusDevice) {
        m_modbusDevice->disconnectDevice();
        emit deviceDisconnected();
        emit connectionStatusChanged(false);
    }
#endif
}


