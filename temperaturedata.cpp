﻿#include "temperaturedata.h"
#include <QFile>
#include <QTextStream>
#include <QDir>
#include <QApplication>
#include <QDebug>
#include <QDataStream>

TemperatureData::TemperatureData(QObject *parent)
    : QObject(parent)
    , m_samplingRate(1000)  // 默认采样率1秒
{
    // 在程序目录下创建数据保存目录
    m_dataPath = QApplication::applicationDirPath() + "/" + DATA_DIR_NAME;
    m_backupPath = QApplication::applicationDirPath() + "/" + BACKUP_DIR_NAME;
    
    QDir dir;
    if (!dir.mkpath(m_dataPath)) {
        qWarning() << "Failed to create data directory:" << m_dataPath;
    } else {
        qDebug() << "Temperature data will be saved to:" << m_dataPath;
    }

    if (!dir.mkpath(m_backupPath)) {
        qWarning() << "Failed to create backup directory:" << m_backupPath;
    }

    // 初始化备份定时器
    m_backupTimer = new QTimer(this);
    connect(m_backupTimer, &QTimer::timeout, this, &TemperatureData::createBackup);
    m_backupTimer->setInterval(3600000); // 每小时备份一次

    // 设置默认的动态阈值
    m_saveThreshold = {
        300,     // 5分钟
        1000,    // 1000个数据点
        100 * 1024 * 1024,  // 100MB
        100     // 最多100个文件
    };
}

void TemperatureData::addTemperatureData(const QVector<float>& temperatures, bool isManual)
{
    QDateTime now = QDateTime::currentDateTime();
    
    // 预分配空间
    if (m_channelData.size() < temperatures.size()) {
        m_channelData.resize(temperatures.size());
        for (auto& channel : m_channelData) {
            channel.reserve(m_maxDataPoints);
        }
    }
    
    // 批量添加数据
    for (int i = 0; i < temperatures.size(); ++i) {
        DataPoint point = {
            static_cast<double>(temperatures[i]),
            now,
            isManual
        };
        point.checksum = calculateChecksum(point);
        m_channelData[i].append(point);
        
        // 如果超过最大保存点数，移除最旧的数据
        if (m_channelData[i].size() > m_maxDataPoints) {
            m_channelData[i].removeFirst();
        }
    }

    // 更新活跃会话的样本计数
    if (m_currentSession && m_currentSession->isActive) {
        m_currentSession->totalSamples += temperatures.size();
        emit sessionStatsUpdated(m_currentSession->sessionId, m_currentSession->totalSamples);
    }

    // 检查是否需要自动保存
    m_dataCountSinceLastSave++;
    if (m_lastSaveTime.isValid()) {
        int secondsElapsed = m_lastSaveTime.secsTo(now);
        if (m_dataCountSinceLastSave >= m_saveThreshold.dataPointCount ||
            secondsElapsed >= m_saveThreshold.timeInterval) {
            autoSaveSegment();
        }
    } else {
        m_lastSaveTime = now;
    }

    emit systemStatusUpdate(QString("已添加 %1 个数据点").arg(temperatures.size()));
}

QString TemperatureData::calculateChecksum(const DataPoint& point) const
{
    QByteArray data;
    QDataStream stream(&data, QIODevice::WriteOnly);
    stream << point.value << point.timestamp << point.isManual;
    return QString(QCryptographicHash::hash(data, QCryptographicHash::Sha256).toHex());
}

bool TemperatureData::validateDataPoint(const DataPoint& point) const
{
    QString calculatedChecksum = calculateChecksum(point);
    return calculatedChecksum == point.checksum;
}

void TemperatureData::autoSaveSegment()
{
    QDir dir(m_dataPath);
    if (!dir.exists()) {
        dir.mkpath(".");
    }

    // 生成文件名（按日期分类）
    QString dateDir = m_dataPath + "/" + QDateTime::currentDateTime().toString("yyyy-MM-dd");
    QDir().mkpath(dateDir);
    
    QString fileName = dateDir + "/temp_data_" + 
                      QDateTime::currentDateTime().toString("hh-mm-ss") + ".csv";

    bool success = saveSegmentToCSV(fileName);
    if (success) {
        m_dataCountSinceLastSave = 0;
        m_lastSaveTime = QDateTime::currentDateTime();
        cleanupOldFiles();
    }
    
    emit dataSaved(success);
    logSystemStatus(QString("数据已保存到: %1").arg(fileName));
}

void TemperatureData::cleanupOldFiles()
{
    QDir dataDir(m_dataPath);
    QFileInfoList dateDirs = dataDir.entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot);

    // 按日期清理文件
    for (const QFileInfo& dateDir : dateDirs) {
        QDir dir(dateDir.absoluteFilePath());
        QFileInfoList files = dir.entryInfoList({"*.csv"}, QDir::Files, QDir::Time);

        // 检查文件数量限制
        if (files.size() > m_saveThreshold.maxFileCount) {
            for (int i = m_saveThreshold.maxFileCount; i < files.size(); ++i) {
                QFile::remove(files[i].absoluteFilePath());
            }
        }

        // 检查文件大小限制
        qint64 totalSize = 0;
        for (const QFileInfo& file : files) {
            totalSize += file.size();
        }

        if (totalSize > m_saveThreshold.maxFileSize) {
            for (int i = files.size() - 1; i >= 0 && totalSize > m_saveThreshold.maxFileSize; --i) {
                totalSize -= files[i].size();
                QFile::remove(files[i].absoluteFilePath());
            }
        }
    }
}

bool TemperatureData::createBackup()
{
    QString backupFileName = m_backupPath + "/backup_" + 
                            QDateTime::currentDateTime().toString("yyyy-MM-dd_hh-mm-ss") + ".csv";
    
    bool success = saveToCSV(backupFileName);
    emit backupCompleted(success);
    logSystemStatus(QString("备份已创建: %1").arg(backupFileName));
    return success;
}

bool TemperatureData::verifyDataIntegrity()
{
    bool integrity = true;
    for (const auto& channel : m_channelData) {
        for (const auto& point : channel) {
            if (!validateDataPoint(point)) {
                emit dataIntegrityError(QString("数据完整性检查失败: %1").arg(point.timestamp.toString()));
                integrity = false;
            }
        }
    }
    return integrity;
}

void TemperatureData::logSystemStatus(const QString& status)
{
    qDebug() << "[System Status]" << QDateTime::currentDateTime().toString() << "-" << status;
    emit systemStatusUpdate(status);
}

bool TemperatureData::restoreFromBackup(const QString& backupFile)
{
    // TODO: 实现从备份文件恢复数据的功能
    return false;
}

QList<double> TemperatureData::data() const {
    return m_data;
}

bool TemperatureData::saveToCSV(const QString& fileName)
{
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit systemStatusUpdate(QString("无法打开文件保存: %1").arg(fileName));
        return false;
    }

    QTextStream out(&file);
    
    // 写入表头
    out << "时间戳,通道,温度值,是否手动记录,校验和\n";
    
    // 写入所有通道的数据
    for (int i = 0; i < m_channelData.size(); ++i) {
        for (const auto& point : m_channelData[i]) {
            out << point.timestamp.toString("yyyy-MM-dd hh:mm:ss.zzz") << ","
                << i + 1 << ","
                << QString::number(point.value, 'f', 2) << ","
                << (point.isManual ? "是" : "否") << ","
                << point.checksum << "\n";
        }
    }
    
    file.close();
    return true;
}

bool TemperatureData::saveSegmentToCSV(const QString& fileName)
{
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit systemStatusUpdate(QString("无法打开文件保存片段: %1").arg(fileName));
        return false;
    }

    QTextStream out(&file);
    
    // 写入表头
    out << "时间戳,通道,温度值,是否手动记录,校验和\n";
    
    // 获取最近的数据点
    int pointsToSave = m_dataCountSinceLastSave;
    
    // 写入所有通道的最新数据
    for (int i = 0; i < m_channelData.size(); ++i) {
        const auto& channelData = m_channelData[i];
        int startIndex = std::max(0, channelData.size() - pointsToSave);
        
        for (int j = startIndex; j < channelData.size(); ++j) {
            const auto& point = channelData[j];
            out << point.timestamp.toString("yyyy-MM-dd hh:mm:ss.zzz") << ","
                << i + 1 << ","
                << QString::number(point.value, 'f', 2) << ","
                << (point.isManual ? "是" : "否") << ","
                << point.checksum << "\n";
        }
    }
    
    file.close();
    return true;
}

void TemperatureData::clearData()
{
    // 清空所有通道数据
    for (auto& channel : m_channelData) {
        channel.clear();
    }
    
    // 重置计数器和时间
    m_dataCountSinceLastSave = 0;
    m_lastSaveTime = QDateTime();
    
    emit systemStatusUpdate("数据已清空");
}

//==============================================================================
// 会话管理方法实现
//==============================================================================

QString TemperatureData::startNewSession(const QString& sessionName)
{
    // 如果已有活跃会话，先结束它
    if (m_currentSession && m_currentSession->isActive) {
        endCurrentSession();
    }
    
    // 生成唯一的会话ID
    QString sessionId = sessionName.isEmpty() ? 
        QString("Session_%1_%2").arg(++m_sessionCounter).arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")) :
        QString("%1_%2").arg(sessionName).arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss"));
    
    // 创建新会话
    AcquisitionSession newSession(sessionId);
    newSession.intervalHistory.append(SamplingIntervalChange(
        QDateTime::currentDateTime(), 0, m_samplingInterval, "Session started"));
    
    m_sessionHistory.append(newSession);
    m_currentSession = &m_sessionHistory.last();
    
    // 重置数据计数
    m_dataCountSinceLastSave = 0;
    
    emit sessionStarted(sessionId);
    logSystemStatus(QString("新会话已开始: %1").arg(sessionId));
    
    return sessionId;
}

bool TemperatureData::endCurrentSession()
{
    if (!m_currentSession || !m_currentSession->isActive) {
        return false;
    }
    
    m_currentSession->endTime = QDateTime::currentDateTime();
    m_currentSession->isActive = false;
    
    int duration = m_currentSession->getDurationSeconds();
    QString sessionId = m_currentSession->sessionId;
    
    emit sessionEnded(sessionId, duration);
    logSystemStatus(QString("会话已结束: %1, 持续时间: %2 秒").arg(sessionId).arg(duration));
    
    m_currentSession = nullptr;
    return true;
}

bool TemperatureData::changeSamplingInterval(int newInterval, const QString& reason)
{
    if (newInterval <= 0) {
        logSystemStatus("无效的采样间隔: " + QString::number(newInterval));
        return false;
    }
    
    int oldInterval = m_samplingInterval;
    m_samplingInterval = newInterval;
    
    // 如果有活跃会话，记录间隔变更
    if (m_currentSession && m_currentSession->isActive) {
        SamplingIntervalChange change(QDateTime::currentDateTime(), oldInterval, newInterval, reason);
        m_currentSession->intervalHistory.append(change);
        
        emit sessionStatsUpdated(m_currentSession->sessionId, m_currentSession->totalSamples);
    }
    
    emit samplingIntervalChanged(oldInterval, newInterval, reason);
    logSystemStatus(QString("采样间隔已更改: %1ms -> %2ms, 原因: %3")
                   .arg(oldInterval).arg(newInterval).arg(reason));
    
    return true;
}

const TemperatureData::AcquisitionSession* TemperatureData::getCurrentSession() const
{
    return m_currentSession;
}

QVector<TemperatureData::AcquisitionSession> TemperatureData::getSessionHistory() const
{
    return m_sessionHistory;
}

int TemperatureData::getActiveSessionCount() const
{
    int count = 0;
    for (const auto& session : m_sessionHistory) {
        if (session.isActive) {
            count++;
        }
    }
    return count;
}

void TemperatureData::clearSessionHistory()
{
    // 只清除非活跃的会话
    auto it = m_sessionHistory.begin();
    while (it != m_sessionHistory.end()) {
        if (!it->isActive) {
            it = m_sessionHistory.erase(it);
        } else {
            ++it;
        }
    }
    
    logSystemStatus("已清除历史会话记录");
}

QString TemperatureData::getCurrentSessionInfo() const
{
    if (!m_currentSession || !m_currentSession->isActive) {
        return "无活跃会话";
    }
    
    int duration = m_currentSession->getDurationSeconds();
    int hours = duration / 3600;
    int minutes = (duration % 3600) / 60;
    int seconds = duration % 60;
    
    QString durationStr = QString("%1:%2:%3")
                         .arg(hours, 2, 10, QChar('0'))
                         .arg(minutes, 2, 10, QChar('0'))
                         .arg(seconds, 2, 10, QChar('0'));
    
    return QString("会话: %1 | 时长: %2 | 采样: %3个 | 间隔: %4ms")
           .arg(m_currentSession->sessionId)
           .arg(durationStr)
           .arg(m_currentSession->totalSamples)
           .arg(m_currentSession->getCurrentInterval());
}

QString TemperatureData::getSessionSummary(const QString& sessionId) const
{
    for (const auto& session : m_sessionHistory) {
        if (session.sessionId == sessionId) {
            QString summary = QString("会话ID: %1\n").arg(session.sessionId);
            summary += QString("开始时间: %1\n").arg(session.startTime.toString("yyyy-MM-dd hh:mm:ss"));
            
            if (session.isActive) {
                summary += "状态: 进行中\n";
                summary += QString("当前持续时间: %1 秒\n").arg(session.getDurationSeconds());
            } else {
                summary += QString("结束时间: %1\n").arg(session.endTime.toString("yyyy-MM-dd hh:mm:ss"));
                summary += QString("总持续时间: %1 秒\n").arg(session.getDurationSeconds());
            }
            
            summary += QString("总采样数: %1\n").arg(session.totalSamples);
            summary += QString("间隔变更次数: %1\n").arg(session.intervalHistory.size());
            
            if (!session.intervalHistory.isEmpty()) {
                summary += "间隔变更历史:\n";
                for (const auto& change : session.intervalHistory) {
                    summary += QString("  %1: %2ms -> %3ms (%4)\n")
                              .arg(change.changeTime.toString("hh:mm:ss"))
                              .arg(change.oldInterval)
                              .arg(change.newInterval)
                              .arg(change.reason);
                }
            }
            
            return summary;
        }
    }
    
    return QString("未找到会话: %1").arg(sessionId);
}
