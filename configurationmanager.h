#ifndef CONFIGURATIONMANAGER_H
#define CONFIGURATIONMANAGER_H

#include <QObject>
#include <QString>
#include <QJsonObject>
#include <QJsonDocument>
#include <QColor>
#include <QRect>
#include <QSerialPort>
#include <QVariantMap>

// 配置项基类
class ConfigItem {
public:
    virtual ~ConfigItem() = default;
    virtual QJsonObject toJson() const = 0;
    virtual void fromJson(const QJsonObject& json) = 0;
    virtual bool validate() const = 0;
    virtual void setDefaults() = 0;
};

// 串口配置
class SerialPortConfig : public ConfigItem {
public:
    QString portName = "COM1";
    int baudRate = 115200;
    QSerialPort::DataBits dataBits = QSerialPort::Data8;
    QSerialPort::Parity parity = QSerialPort::NoParity;
    QSerialPort::StopBits stopBits = QSerialPort::OneStop;
    QSerialPort::FlowControl flowControl = QSerialPort::NoFlowControl;
    int slaveAddress = 1;
    int timeoutMs = 3000;
    int retryCount = 3;

    QJsonObject toJson() const override;
    void fromJson(const QJsonObject& json) override;
    bool validate() const override;
    void setDefaults() override;
};

// 数据采集配置
class DataAcquisitionConfig : public ConfigItem {
public:
    int samplingIntervalMs = 1000;
    int maxDataPoints = 3600;
    int temperatureChannels = 10;
    bool autoSave = true;
    int autoSaveIntervalMin = 10;
    QString dataPath = "./temperature_data";
    QString backupPath = "./backup_data";
    int maxFileSize = 10 * 1024 * 1024; // 10MB
    int maxFileCount = 100;

    QJsonObject toJson() const override;
    void fromJson(const QJsonObject& json) override;
    bool validate() const override;
    void setDefaults() override;
};

// 图表显示配置
class ChartDisplayConfig : public ConfigItem {
public:
    int plotRows = 5;
    int plotColumns = 2;
    bool openGLEnabled = true;
    bool antialiasing = false;
    bool adaptiveSampling = true;
    int lineWidth = 1;
    double temperatureMin = -200.0;
    double temperatureMax = 50.0;
    double timeRangeSeconds = 100.0;
    QStringList channelColors = {
        "#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF",
        "#00FFFF", "#FFA500", "#800080", "#008000", "#800000"
    };

    QJsonObject toJson() const override;
    void fromJson(const QJsonObject& json) override;
    bool validate() const override;
    void setDefaults() override;
};

// UI布局配置
class UILayoutConfig : public ConfigItem {
public:
    QRect windowGeometry = QRect(100, 100, 1579, 766);
    bool maximized = false;
    QVariantMap panelStates; // 存储各面板的状态
    QVariantMap splitterStates; // 存储分割器状态
    QString layoutName = "default";
    int version = 1;

    QJsonObject toJson() const override;
    void fromJson(const QJsonObject& json) override;
    bool validate() const override;
    void setDefaults() override;
};

// 导出配置
class ExportConfig : public ConfigItem {
public:
    QString defaultFormat = "CSV";
    QStringList availableFormats = {"CSV", "JSON", "XML", "Excel"};
    QString exportPath = "./exports";
    bool includeHeader = true;
    QString dateTimeFormat = "yyyy-MM-dd hh:mm:ss.zzz";
    QString csvSeparator = ",";
    bool compressFiles = false;

    QJsonObject toJson() const override;
    void fromJson(const QJsonObject& json) override;
    bool validate() const override;
    void setDefaults() override;
};

// 主配置管理器
class ConfigurationManager : public QObject {
    Q_OBJECT

public:
    explicit ConfigurationManager(QObject *parent = nullptr);
    ~ConfigurationManager();

    // 单例模式
    static ConfigurationManager* instance();

    // 核心功能
    bool loadFromFile(const QString& configPath = QString());
    bool saveToFile(const QString& configPath = QString()) const;
    bool validateAllConfigs() const;
    void resetToDefaults();

    // 配置访问器
    SerialPortConfig& serialPortConfig() { return m_serialPortConfig; }
    const SerialPortConfig& serialPortConfig() const { return m_serialPortConfig; }

    DataAcquisitionConfig& dataAcquisitionConfig() { return m_dataAcquisitionConfig; }
    const DataAcquisitionConfig& dataAcquisitionConfig() const { return m_dataAcquisitionConfig; }

    ChartDisplayConfig& chartDisplayConfig() { return m_chartDisplayConfig; }
    const ChartDisplayConfig& chartDisplayConfig() const { return m_chartDisplayConfig; }

    UILayoutConfig& uiLayoutConfig() { return m_uiLayoutConfig; }
    const UILayoutConfig& uiLayoutConfig() const { return m_uiLayoutConfig; }

    ExportConfig& exportConfig() { return m_exportConfig; }
    const ExportConfig& exportConfig() const { return m_exportConfig; }

    // 热更新支持
    void markDirty() { m_isDirty = true; }
    bool isDirty() const { return m_isDirty; }
    void clearDirty() { m_isDirty = false; }

    // 配置路径管理
    void setConfigPath(const QString& path);
    QString getConfigPath() const;

    // 版本管理
    int getConfigVersion() const { return m_configVersion; }
    bool isUpgradeNeeded() const;
    bool upgradeConfig();

signals:
    void configChanged(const QString& section);
    void configSaved();
    void configLoaded();
    void validationError(const QString& error);

private:
    void initializeDefaults();
    bool migrateFromLegacySettings();
    QString getDefaultConfigPath() const;

private:
    static ConfigurationManager* s_instance;
    
    SerialPortConfig m_serialPortConfig;
    DataAcquisitionConfig m_dataAcquisitionConfig;
    ChartDisplayConfig m_chartDisplayConfig;
    UILayoutConfig m_uiLayoutConfig;
    ExportConfig m_exportConfig;

    QString m_configPath;
    bool m_isDirty = false;
    int m_configVersion = 1;
    static const int CURRENT_CONFIG_VERSION = 1;
};

#endif // CONFIGURATIONMANAGER_H 