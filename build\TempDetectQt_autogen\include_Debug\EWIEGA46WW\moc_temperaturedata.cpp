/****************************************************************************
** Meta object code from reading C++ file 'temperaturedata.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../temperaturedata.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#include <QtCore/QVector>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'temperaturedata.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_TemperatureData_t {
    QByteArrayData data[60];
    char stringdata0[869];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_TemperatureData_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_TemperatureData_t qt_meta_stringdata_TemperatureData = {
    {
QT_MOC_LITERAL(0, 0, 15), // "TemperatureData"
QT_MOC_LITERAL(1, 16, 9), // "dataSaved"
QT_MOC_LITERAL(2, 26, 0), // ""
QT_MOC_LITERAL(3, 27, 7), // "success"
QT_MOC_LITERAL(4, 35, 15), // "backupCompleted"
QT_MOC_LITERAL(5, 51, 18), // "dataIntegrityError"
QT_MOC_LITERAL(6, 70, 5), // "error"
QT_MOC_LITERAL(7, 76, 18), // "systemStatusUpdate"
QT_MOC_LITERAL(8, 95, 6), // "status"
QT_MOC_LITERAL(9, 102, 14), // "sessionStarted"
QT_MOC_LITERAL(10, 117, 9), // "sessionId"
QT_MOC_LITERAL(11, 127, 12), // "sessionEnded"
QT_MOC_LITERAL(12, 140, 15), // "durationSeconds"
QT_MOC_LITERAL(13, 156, 23), // "samplingIntervalChanged"
QT_MOC_LITERAL(14, 180, 11), // "oldInterval"
QT_MOC_LITERAL(15, 192, 11), // "newInterval"
QT_MOC_LITERAL(16, 204, 6), // "reason"
QT_MOC_LITERAL(17, 211, 19), // "sessionStatsUpdated"
QT_MOC_LITERAL(18, 231, 12), // "totalSamples"
QT_MOC_LITERAL(19, 244, 18), // "addTemperatureData"
QT_MOC_LITERAL(20, 263, 14), // "QVector<float>"
QT_MOC_LITERAL(21, 278, 12), // "temperatures"
QT_MOC_LITERAL(22, 291, 8), // "isManual"
QT_MOC_LITERAL(23, 300, 9), // "saveToCSV"
QT_MOC_LITERAL(24, 310, 8), // "fileName"
QT_MOC_LITERAL(25, 319, 16), // "saveSegmentToCSV"
QT_MOC_LITERAL(26, 336, 9), // "clearData"
QT_MOC_LITERAL(27, 346, 15), // "setSamplingRate"
QT_MOC_LITERAL(28, 362, 2), // "ms"
QT_MOC_LITERAL(29, 365, 16), // "setMaxDataPoints"
QT_MOC_LITERAL(30, 382, 6), // "points"
QT_MOC_LITERAL(31, 389, 15), // "autoSaveSegment"
QT_MOC_LITERAL(32, 405, 19), // "setSamplingInterval"
QT_MOC_LITERAL(33, 425, 8), // "interval"
QT_MOC_LITERAL(34, 434, 20), // "setTotalSamplingTime"
QT_MOC_LITERAL(35, 455, 9), // "totalTime"
QT_MOC_LITERAL(36, 465, 16), // "setSaveThreshold"
QT_MOC_LITERAL(37, 482, 13), // "SaveThreshold"
QT_MOC_LITERAL(38, 496, 9), // "threshold"
QT_MOC_LITERAL(39, 506, 15), // "startAutoBackup"
QT_MOC_LITERAL(40, 522, 14), // "stopAutoBackup"
QT_MOC_LITERAL(41, 537, 17), // "restoreFromBackup"
QT_MOC_LITERAL(42, 555, 10), // "backupFile"
QT_MOC_LITERAL(43, 566, 19), // "verifyDataIntegrity"
QT_MOC_LITERAL(44, 586, 15), // "startNewSession"
QT_MOC_LITERAL(45, 602, 11), // "sessionName"
QT_MOC_LITERAL(46, 614, 17), // "endCurrentSession"
QT_MOC_LITERAL(47, 632, 22), // "changeSamplingInterval"
QT_MOC_LITERAL(48, 655, 17), // "getCurrentSession"
QT_MOC_LITERAL(49, 673, 25), // "const AcquisitionSession*"
QT_MOC_LITERAL(50, 699, 17), // "getSessionHistory"
QT_MOC_LITERAL(51, 717, 27), // "QVector<AcquisitionSession>"
QT_MOC_LITERAL(52, 745, 21), // "getActiveSessionCount"
QT_MOC_LITERAL(53, 767, 19), // "clearSessionHistory"
QT_MOC_LITERAL(54, 787, 21), // "getCurrentSessionInfo"
QT_MOC_LITERAL(55, 809, 17), // "getSessionSummary"
QT_MOC_LITERAL(56, 827, 14), // "getChannelData"
QT_MOC_LITERAL(57, 842, 7), // "channel"
QT_MOC_LITERAL(58, 850, 4), // "data"
QT_MOC_LITERAL(59, 855, 13) // "QList<double>"

    },
    "TemperatureData\0dataSaved\0\0success\0"
    "backupCompleted\0dataIntegrityError\0"
    "error\0systemStatusUpdate\0status\0"
    "sessionStarted\0sessionId\0sessionEnded\0"
    "durationSeconds\0samplingIntervalChanged\0"
    "oldInterval\0newInterval\0reason\0"
    "sessionStatsUpdated\0totalSamples\0"
    "addTemperatureData\0QVector<float>\0"
    "temperatures\0isManual\0saveToCSV\0"
    "fileName\0saveSegmentToCSV\0clearData\0"
    "setSamplingRate\0ms\0setMaxDataPoints\0"
    "points\0autoSaveSegment\0setSamplingInterval\0"
    "interval\0setTotalSamplingTime\0totalTime\0"
    "setSaveThreshold\0SaveThreshold\0threshold\0"
    "startAutoBackup\0stopAutoBackup\0"
    "restoreFromBackup\0backupFile\0"
    "verifyDataIntegrity\0startNewSession\0"
    "sessionName\0endCurrentSession\0"
    "changeSamplingInterval\0getCurrentSession\0"
    "const AcquisitionSession*\0getSessionHistory\0"
    "QVector<AcquisitionSession>\0"
    "getActiveSessionCount\0clearSessionHistory\0"
    "getCurrentSessionInfo\0getSessionSummary\0"
    "getChannelData\0channel\0data\0QList<double>"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_TemperatureData[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      36,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       8,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,  194,    2, 0x06 /* Public */,
       4,    1,  197,    2, 0x06 /* Public */,
       5,    1,  200,    2, 0x06 /* Public */,
       7,    1,  203,    2, 0x06 /* Public */,
       9,    1,  206,    2, 0x06 /* Public */,
      11,    2,  209,    2, 0x06 /* Public */,
      13,    3,  214,    2, 0x06 /* Public */,
      17,    2,  221,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      19,    2,  226,    2, 0x0a /* Public */,
      19,    1,  231,    2, 0x2a /* Public | MethodCloned */,
      23,    1,  234,    2, 0x0a /* Public */,
      25,    1,  237,    2, 0x0a /* Public */,
      26,    0,  240,    2, 0x0a /* Public */,
      27,    1,  241,    2, 0x0a /* Public */,
      29,    1,  244,    2, 0x0a /* Public */,
      31,    0,  247,    2, 0x0a /* Public */,
      32,    1,  248,    2, 0x0a /* Public */,
      34,    1,  251,    2, 0x0a /* Public */,
      36,    1,  254,    2, 0x0a /* Public */,
      39,    0,  257,    2, 0x0a /* Public */,
      40,    0,  258,    2, 0x0a /* Public */,
      41,    1,  259,    2, 0x0a /* Public */,
      43,    0,  262,    2, 0x0a /* Public */,
      44,    1,  263,    2, 0x0a /* Public */,
      44,    0,  266,    2, 0x2a /* Public | MethodCloned */,
      46,    0,  267,    2, 0x0a /* Public */,
      47,    2,  268,    2, 0x0a /* Public */,
      47,    1,  273,    2, 0x2a /* Public | MethodCloned */,
      48,    0,  276,    2, 0x0a /* Public */,
      50,    0,  277,    2, 0x0a /* Public */,
      52,    0,  278,    2, 0x0a /* Public */,
      53,    0,  279,    2, 0x0a /* Public */,
      54,    0,  280,    2, 0x0a /* Public */,
      55,    1,  281,    2, 0x0a /* Public */,
      56,    1,  284,    2, 0x0a /* Public */,
      58,    0,  287,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::Bool,    3,
    QMetaType::Void, QMetaType::Bool,    3,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void, QMetaType::QString,    8,
    QMetaType::Void, QMetaType::QString,   10,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   10,   12,
    QMetaType::Void, QMetaType::Int, QMetaType::Int, QMetaType::QString,   14,   15,   16,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   10,   18,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 20, QMetaType::Bool,   21,   22,
    QMetaType::Void, 0x80000000 | 20,   21,
    QMetaType::Bool, QMetaType::QString,   24,
    QMetaType::Bool, QMetaType::QString,   24,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   28,
    QMetaType::Void, QMetaType::Int,   30,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   33,
    QMetaType::Void, QMetaType::Int,   35,
    QMetaType::Void, 0x80000000 | 37,   38,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Bool, QMetaType::QString,   42,
    QMetaType::Bool,
    QMetaType::QString, QMetaType::QString,   45,
    QMetaType::QString,
    QMetaType::Bool,
    QMetaType::Bool, QMetaType::Int, QMetaType::QString,   15,   16,
    QMetaType::Bool, QMetaType::Int,   15,
    0x80000000 | 49,
    0x80000000 | 51,
    QMetaType::Int,
    QMetaType::Void,
    QMetaType::QString,
    QMetaType::QString, QMetaType::QString,   10,
    QMetaType::Void, QMetaType::Int,   57,
    0x80000000 | 59,

       0        // eod
};

void TemperatureData::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<TemperatureData *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->dataSaved((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 1: _t->backupCompleted((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 2: _t->dataIntegrityError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 3: _t->systemStatusUpdate((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 4: _t->sessionStarted((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 5: _t->sessionEnded((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 6: _t->samplingIntervalChanged((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3]))); break;
        case 7: _t->sessionStatsUpdated((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 8: _t->addTemperatureData((*reinterpret_cast< const QVector<float>(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 9: _t->addTemperatureData((*reinterpret_cast< const QVector<float>(*)>(_a[1]))); break;
        case 10: { bool _r = _t->saveToCSV((*reinterpret_cast< const QString(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 11: { bool _r = _t->saveSegmentToCSV((*reinterpret_cast< const QString(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 12: _t->clearData(); break;
        case 13: _t->setSamplingRate((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 14: _t->setMaxDataPoints((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 15: _t->autoSaveSegment(); break;
        case 16: _t->setSamplingInterval((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 17: _t->setTotalSamplingTime((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 18: _t->setSaveThreshold((*reinterpret_cast< const SaveThreshold(*)>(_a[1]))); break;
        case 19: _t->startAutoBackup(); break;
        case 20: _t->stopAutoBackup(); break;
        case 21: { bool _r = _t->restoreFromBackup((*reinterpret_cast< const QString(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 22: { bool _r = _t->verifyDataIntegrity();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 23: { QString _r = _t->startNewSession((*reinterpret_cast< const QString(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 24: { QString _r = _t->startNewSession();
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 25: { bool _r = _t->endCurrentSession();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 26: { bool _r = _t->changeSamplingInterval((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 27: { bool _r = _t->changeSamplingInterval((*reinterpret_cast< int(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 28: { const AcquisitionSession* _r = _t->getCurrentSession();
            if (_a[0]) *reinterpret_cast< const AcquisitionSession**>(_a[0]) = std::move(_r); }  break;
        case 29: { QVector<AcquisitionSession> _r = _t->getSessionHistory();
            if (_a[0]) *reinterpret_cast< QVector<AcquisitionSession>*>(_a[0]) = std::move(_r); }  break;
        case 30: { int _r = _t->getActiveSessionCount();
            if (_a[0]) *reinterpret_cast< int*>(_a[0]) = std::move(_r); }  break;
        case 31: _t->clearSessionHistory(); break;
        case 32: { QString _r = _t->getCurrentSessionInfo();
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 33: { QString _r = _t->getSessionSummary((*reinterpret_cast< const QString(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 34: _t->getChannelData((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 35: { QList<double> _r = _t->data();
            if (_a[0]) *reinterpret_cast< QList<double>*>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 8:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QVector<float> >(); break;
            }
            break;
        case 9:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QVector<float> >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (TemperatureData::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TemperatureData::dataSaved)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (TemperatureData::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TemperatureData::backupCompleted)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (TemperatureData::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TemperatureData::dataIntegrityError)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (TemperatureData::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TemperatureData::systemStatusUpdate)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (TemperatureData::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TemperatureData::sessionStarted)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (TemperatureData::*)(const QString & , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TemperatureData::sessionEnded)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (TemperatureData::*)(int , int , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TemperatureData::samplingIntervalChanged)) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (TemperatureData::*)(const QString & , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&TemperatureData::sessionStatsUpdated)) {
                *result = 7;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject TemperatureData::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_TemperatureData.data,
    qt_meta_data_TemperatureData,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *TemperatureData::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *TemperatureData::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_TemperatureData.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int TemperatureData::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 36)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 36;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 36)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 36;
    }
    return _id;
}

// SIGNAL 0
void TemperatureData::dataSaved(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void TemperatureData::backupCompleted(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void TemperatureData::dataIntegrityError(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void TemperatureData::systemStatusUpdate(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void TemperatureData::sessionStarted(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void TemperatureData::sessionEnded(const QString & _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void TemperatureData::samplingIntervalChanged(int _t1, int _t2, const QString & _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void TemperatureData::sessionStatsUpdated(const QString & _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
