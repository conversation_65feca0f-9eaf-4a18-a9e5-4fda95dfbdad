cmake_minimum_required(VERSION 3.17)
project(S4)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 设置 Qt5 安装路径
set(CMAKE_PREFIX_PATH "C:/Qt/5.15.0/mingw81_64")

# 查找 Qt5 组件
find_package(Qt5 REQUIRED COMPONENTS Core Gui Widgets PrintSupport OpenGL SerialPort SerialBus)

# 源文件和头文件
set(SOURCES
        main.cpp
        mainwindow.cpp
        modbusmanager.cpp
        qcustomplot.cpp
        temperaturedata.cpp
        configurationmanager.cpp
)

set(HEADERS
        mainwindow.h
        qcustomplot.h
        modbusmanager.h
        temperaturedata.h
        configurationmanager.h
)

set(FORMS
        mainwindow.ui
)

# 添加 UI 文件
qt5_wrap_ui(FORMS_HEADERS ${FORMS})

# 生成可执行文件
add_executable(TempDetectQt ${SOURCES} ${HEADERS} ${FORMS_HEADERS})

# 头文件路径
include_directories(
        ${CMAKE_SOURCE_DIR}
        /opt/homebrew/Cellar/qt@5/5.15.16/include
        /opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialBus
        /opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialPort
)

# 链接 Qt5 库
target_link_libraries(TempDetectQt
        Qt5::Core
        Qt5::Gui
        Qt5::Widgets
        Qt5::PrintSupport
        Qt5::OpenGL
        Qt5::SerialPort
        Qt5::SerialBus
)
