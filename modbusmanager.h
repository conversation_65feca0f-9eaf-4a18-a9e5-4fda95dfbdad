﻿#ifndef MODBUSMANAGER_H
#define MODBUSMANAGER_H

// 在文件开头添加测试模式宏定义
#define MODBUS_TEST_MODE 1  // 设置为1启用测试模式，0为正常模式

#include <QObject>
#include <QModbusRtuSerialMaster>
#include <QSerialPort>

class ModbusManager : public QObject
{
    Q_OBJECT

public:
    explicit ModbusManager(QObject *parent = nullptr);
    ~ModbusManager();

    // 串口配置结构体
    struct SerialConfig {
        QString portName;
        int baudRate;
        QSerialPort::DataBits dataBits;
        QSerialPort::Parity parity;
        QSerialPort::StopBits stopBits;
        QSerialPort::FlowControl flowControl;
    };

public slots:
    bool connectDevice(const SerialConfig& config);
    void disconnectDevice();
    void readTemperatures(int slaveAddress);

signals:
    void connectionStatusChanged(bool connected);
    void temperaturesRead(const QVector<float>& temperatures);
    void errorOccurred(const QString& error);
    void deviceConnected();
    void deviceDisconnected();

private:
    void handleModbusResponse(QModbusReply* reply);
    float convertRegisterToTemperature(quint16 registerValue);

private:
    QModbusRtuSerialMaster* m_modbusDevice;
    static const int TIMEOUT_MS = 1000;
    static const int RETRY_COUNT = 3;
    static const int REGISTER_START_ADDRESS = 0;
    static const int REGISTER_COUNT = 10;

public:
    QModbusDevice::State deviceState() const {
        return m_modbusDevice ? m_modbusDevice->state() : QModbusDevice::UnconnectedState;
    }

};

#endif // MODBUSMANAGER_H 
