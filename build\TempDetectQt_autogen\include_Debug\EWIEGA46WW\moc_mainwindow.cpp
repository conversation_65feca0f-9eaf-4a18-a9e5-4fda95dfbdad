/****************************************************************************
** Meta object code from reading C++ file 'mainwindow.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../../mainwindow.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#include <QtCore/QVector>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'mainwindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_MainWindow_t {
    QByteArrayData data[19];
    char stringdata0[287];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_MainWindow_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_MainWindow_t qt_meta_stringdata_MainWindow = {
    {
QT_MOC_LITERAL(0, 0, 10), // "MainWindow"
QT_MOC_LITERAL(1, 11, 17), // "onOpenPortClicked"
QT_MOC_LITERAL(2, 29, 0), // ""
QT_MOC_LITERAL(3, 30, 17), // "onReadDataClicked"
QT_MOC_LITERAL(4, 48, 17), // "onSaveDataClicked"
QT_MOC_LITERAL(5, 66, 19), // "onTimerStateChanged"
QT_MOC_LITERAL(6, 86, 5), // "state"
QT_MOC_LITERAL(7, 92, 24), // "updateTemperatureDisplay"
QT_MOC_LITERAL(8, 117, 14), // "QVector<float>"
QT_MOC_LITERAL(9, 132, 12), // "temperatures"
QT_MOC_LITERAL(10, 145, 22), // "updateConnectionStatus"
QT_MOC_LITERAL(11, 168, 9), // "connected"
QT_MOC_LITERAL(12, 178, 17), // "showStatusMessage"
QT_MOC_LITERAL(13, 196, 7), // "message"
QT_MOC_LITERAL(14, 204, 7), // "timeout"
QT_MOC_LITERAL(15, 212, 10), // "updatePlot"
QT_MOC_LITERAL(16, 223, 24), // "on_btn_resettime_clicked"
QT_MOC_LITERAL(17, 248, 17), // "onDeviceConnected"
QT_MOC_LITERAL(18, 266, 20) // "onDeviceDisconnected"

    },
    "MainWindow\0onOpenPortClicked\0\0"
    "onReadDataClicked\0onSaveDataClicked\0"
    "onTimerStateChanged\0state\0"
    "updateTemperatureDisplay\0QVector<float>\0"
    "temperatures\0updateConnectionStatus\0"
    "connected\0showStatusMessage\0message\0"
    "timeout\0updatePlot\0on_btn_resettime_clicked\0"
    "onDeviceConnected\0onDeviceDisconnected"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_MainWindow[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      12,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   74,    2, 0x08 /* Private */,
       3,    0,   75,    2, 0x08 /* Private */,
       4,    0,   76,    2, 0x08 /* Private */,
       5,    1,   77,    2, 0x08 /* Private */,
       7,    1,   80,    2, 0x08 /* Private */,
      10,    1,   83,    2, 0x08 /* Private */,
      12,    2,   86,    2, 0x08 /* Private */,
      12,    1,   91,    2, 0x28 /* Private | MethodCloned */,
      15,    0,   94,    2, 0x08 /* Private */,
      16,    0,   95,    2, 0x08 /* Private */,
      17,    0,   96,    2, 0x08 /* Private */,
      18,    0,   97,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,    6,
    QMetaType::Void, 0x80000000 | 8,    9,
    QMetaType::Void, QMetaType::Bool,   11,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   13,   14,
    QMetaType::Void, QMetaType::QString,   13,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void MainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<MainWindow *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onOpenPortClicked(); break;
        case 1: _t->onReadDataClicked(); break;
        case 2: _t->onSaveDataClicked(); break;
        case 3: _t->onTimerStateChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 4: _t->updateTemperatureDisplay((*reinterpret_cast< const QVector<float>(*)>(_a[1]))); break;
        case 5: _t->updateConnectionStatus((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 6: _t->showStatusMessage((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 7: _t->showStatusMessage((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 8: _t->updatePlot(); break;
        case 9: _t->on_btn_resettime_clicked(); break;
        case 10: _t->onDeviceConnected(); break;
        case 11: _t->onDeviceDisconnected(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 4:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QVector<float> >(); break;
            }
            break;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject MainWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_MainWindow.data,
    qt_meta_data_MainWindow,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *MainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_MainWindow.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int MainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
