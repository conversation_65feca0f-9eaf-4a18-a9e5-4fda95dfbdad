^C:\USERS\<USER>\DESKTOP\TEMPDETECTQT\MAINWINDOW.UI
setlocal
C:\Qt\5.15.0\mingw81_64\bin\uic.exe -o C:/Users/<USER>/Desktop/TempDetectQt/build/ui_mainwindow.h C:/Users/<USER>/Desktop/TempDetectQt/mainwindow.ui
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DESKTOP\TEMPDETECTQT\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/TempDetectQt -BC:/Users/<USER>/Desktop/TempDetectQt/build --check-stamp-file C:/Users/<USER>/Desktop/TempDetectQt/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
