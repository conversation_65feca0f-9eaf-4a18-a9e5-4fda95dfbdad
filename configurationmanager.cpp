#include "configurationmanager.h"
#include <QStandardPaths>
#include <QDir>
#include <QFile>
#include <QJsonDocument>
#include <QJsonArray>
#include <QDebug>
#include <QCoreApplication>
#include <QSettings>
#include <QDateTime>

// 静态成员初始化
ConfigurationManager* ConfigurationManager::s_instance = nullptr;

//==============================================================================
// SerialPortConfig Implementation
//==============================================================================
QJsonObject SerialPortConfig::toJson() const {
    QJsonObject json;
    json["portName"] = portName;
    json["baudRate"] = baudRate;
    json["dataBits"] = static_cast<int>(dataBits);
    json["parity"] = static_cast<int>(parity);
    json["stopBits"] = static_cast<int>(stopBits);
    json["flowControl"] = static_cast<int>(flowControl);
    json["slaveAddress"] = slaveAddress;
    json["timeoutMs"] = timeoutMs;
    json["retryCount"] = retryCount;
    return json;
}

void SerialPortConfig::fromJson(const QJsonObject& json) {
    portName = json["portName"].toString("COM1");
    baudRate = json["baudRate"].toInt(115200);
    dataBits = static_cast<QSerialPort::DataBits>(json["dataBits"].toInt(QSerialPort::Data8));
    parity = static_cast<QSerialPort::Parity>(json["parity"].toInt(QSerialPort::NoParity));
    stopBits = static_cast<QSerialPort::StopBits>(json["stopBits"].toInt(QSerialPort::OneStop));
    flowControl = static_cast<QSerialPort::FlowControl>(json["flowControl"].toInt(QSerialPort::NoFlowControl));
    slaveAddress = json["slaveAddress"].toInt(1);
    timeoutMs = json["timeoutMs"].toInt(3000);
    retryCount = json["retryCount"].toInt(3);
}

bool SerialPortConfig::validate() const {
    if (portName.isEmpty()) return false;
    if (baudRate <= 0) return false;
    if (slaveAddress < 1 || slaveAddress > 247) return false;
    if (timeoutMs < 100 || timeoutMs > 30000) return false;
    if (retryCount < 0 || retryCount > 10) return false;
    return true;
}

void SerialPortConfig::setDefaults() {
    portName = "COM1";
    baudRate = 115200;
    dataBits = QSerialPort::Data8;
    parity = QSerialPort::NoParity;
    stopBits = QSerialPort::OneStop;
    flowControl = QSerialPort::NoFlowControl;
    slaveAddress = 1;
    timeoutMs = 3000;
    retryCount = 3;
}

//==============================================================================
// DataAcquisitionConfig Implementation
//==============================================================================
QJsonObject DataAcquisitionConfig::toJson() const {
    QJsonObject json;
    json["samplingIntervalMs"] = samplingIntervalMs;
    json["maxDataPoints"] = maxDataPoints;
    json["temperatureChannels"] = temperatureChannels;
    json["autoSave"] = autoSave;
    json["autoSaveIntervalMin"] = autoSaveIntervalMin;
    json["dataPath"] = dataPath;
    json["backupPath"] = backupPath;
    json["maxFileSize"] = maxFileSize;
    json["maxFileCount"] = maxFileCount;
    return json;
}

void DataAcquisitionConfig::fromJson(const QJsonObject& json) {
    samplingIntervalMs = json["samplingIntervalMs"].toInt(1000);
    maxDataPoints = json["maxDataPoints"].toInt(3600);
    temperatureChannels = json["temperatureChannels"].toInt(10);
    autoSave = json["autoSave"].toBool(true);
    autoSaveIntervalMin = json["autoSaveIntervalMin"].toInt(10);
    dataPath = json["dataPath"].toString("./temperature_data");
    backupPath = json["backupPath"].toString("./backup_data");
    maxFileSize = json["maxFileSize"].toInt(10 * 1024 * 1024);
    maxFileCount = json["maxFileCount"].toInt(100);
}

bool DataAcquisitionConfig::validate() const {
    if (samplingIntervalMs < 100 || samplingIntervalMs > 60000) return false;
    if (maxDataPoints < 100 || maxDataPoints > 100000) return false;
    if (temperatureChannels < 1 || temperatureChannels > 20) return false;
    if (autoSaveIntervalMin < 1 || autoSaveIntervalMin > 1440) return false;
    if (dataPath.isEmpty() || backupPath.isEmpty()) return false;
    if (maxFileSize < 1024 || maxFileSize > 1024 * 1024 * 1024) return false;
    if (maxFileCount < 1 || maxFileCount > 10000) return false;
    return true;
}

void DataAcquisitionConfig::setDefaults() {
    samplingIntervalMs = 1000;
    maxDataPoints = 3600;
    temperatureChannels = 10;
    autoSave = true;
    autoSaveIntervalMin = 10;
    dataPath = "./temperature_data";
    backupPath = "./backup_data";
    maxFileSize = 10 * 1024 * 1024;
    maxFileCount = 100;
}

//==============================================================================
// ChartDisplayConfig Implementation
//==============================================================================
QJsonObject ChartDisplayConfig::toJson() const {
    QJsonObject json;
    json["plotRows"] = plotRows;
    json["plotColumns"] = plotColumns;
    json["openGLEnabled"] = openGLEnabled;
    json["antialiasing"] = antialiasing;
    json["adaptiveSampling"] = adaptiveSampling;
    json["lineWidth"] = lineWidth;
    json["temperatureMin"] = temperatureMin;
    json["temperatureMax"] = temperatureMax;
    json["timeRangeSeconds"] = timeRangeSeconds;
    
    QJsonArray colorsArray;
    for (const QString& color : channelColors) {
        colorsArray.append(color);
    }
    json["channelColors"] = colorsArray;
    
    return json;
}

void ChartDisplayConfig::fromJson(const QJsonObject& json) {
    plotRows = json["plotRows"].toInt(5);
    plotColumns = json["plotColumns"].toInt(2);
    openGLEnabled = json["openGLEnabled"].toBool(true);
    antialiasing = json["antialiasing"].toBool(false);
    adaptiveSampling = json["adaptiveSampling"].toBool(true);
    lineWidth = json["lineWidth"].toInt(1);
    temperatureMin = json["temperatureMin"].toDouble(-200.0);
    temperatureMax = json["temperatureMax"].toDouble(50.0);
    timeRangeSeconds = json["timeRangeSeconds"].toDouble(100.0);
    
    channelColors.clear();
    QJsonArray colorsArray = json["channelColors"].toArray();
    for (const QJsonValue& value : colorsArray) {
        channelColors.append(value.toString());
    }
    
    // 如果没有颜色或颜色不足，使用默认颜色
    if (channelColors.isEmpty()) {
        setDefaults();
    }
}

bool ChartDisplayConfig::validate() const {
    if (plotRows < 1 || plotRows > 10) return false;
    if (plotColumns < 1 || plotColumns > 10) return false;
    if (lineWidth < 1 || lineWidth > 10) return false;
    if (temperatureMin >= temperatureMax) return false;
    if (timeRangeSeconds < 1.0 || timeRangeSeconds > 86400.0) return false;
    if (channelColors.isEmpty()) return false;
    return true;
}

void ChartDisplayConfig::setDefaults() {
    plotRows = 5;
    plotColumns = 2;
    openGLEnabled = true;
    antialiasing = false;
    adaptiveSampling = true;
    lineWidth = 1;
    temperatureMin = -200.0;
    temperatureMax = 50.0;
    timeRangeSeconds = 100.0;
    channelColors = QStringList{
        "#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF",
        "#00FFFF", "#FFA500", "#800080", "#008000", "#800000"
    };
}

//==============================================================================
// UILayoutConfig Implementation
//==============================================================================
QJsonObject UILayoutConfig::toJson() const {
    QJsonObject json;
    QJsonObject geometryObj;
    geometryObj["x"] = windowGeometry.x();
    geometryObj["y"] = windowGeometry.y();
    geometryObj["width"] = windowGeometry.width();
    geometryObj["height"] = windowGeometry.height();
    json["windowGeometry"] = geometryObj;
    json["maximized"] = maximized;
    json["layoutName"] = layoutName;
    json["version"] = version;
    
    // 转换QVariantMap到QJsonObject
    json["panelStates"] = QJsonObject::fromVariantMap(panelStates);
    json["splitterStates"] = QJsonObject::fromVariantMap(splitterStates);
    
    return json;
}

void UILayoutConfig::fromJson(const QJsonObject& json) {
    QJsonObject geometryObj = json["windowGeometry"].toObject();
    windowGeometry = QRect(
        geometryObj["x"].toInt(100),
        geometryObj["y"].toInt(100),
        geometryObj["width"].toInt(1579),
        geometryObj["height"].toInt(766)
    );
    maximized = json["maximized"].toBool(false);
    layoutName = json["layoutName"].toString("default");
    version = json["version"].toInt(1);
    
    // 转换QJsonObject到QVariantMap
    panelStates = json["panelStates"].toObject().toVariantMap();
    splitterStates = json["splitterStates"].toObject().toVariantMap();
}

bool UILayoutConfig::validate() const {
    if (windowGeometry.width() < 800 || windowGeometry.width() > 4000) return false;
    if (windowGeometry.height() < 600 || windowGeometry.height() > 3000) return false;
    if (layoutName.isEmpty()) return false;
    if (version < 1) return false;
    return true;
}

void UILayoutConfig::setDefaults() {
    windowGeometry = QRect(100, 100, 1579, 766);
    maximized = false;
    layoutName = "default";
    version = 1;
    panelStates.clear();
    splitterStates.clear();
}

//==============================================================================
// ExportConfig Implementation
//==============================================================================
QJsonObject ExportConfig::toJson() const {
    QJsonObject json;
    json["defaultFormat"] = defaultFormat;
    
    QJsonArray formatsArray;
    for (const QString& format : availableFormats) {
        formatsArray.append(format);
    }
    json["availableFormats"] = formatsArray;
    
    json["exportPath"] = exportPath;
    json["includeHeader"] = includeHeader;
    json["dateTimeFormat"] = dateTimeFormat;
    json["csvSeparator"] = csvSeparator;
    json["compressFiles"] = compressFiles;
    
    return json;
}

void ExportConfig::fromJson(const QJsonObject& json) {
    defaultFormat = json["defaultFormat"].toString("CSV");
    
    availableFormats.clear();
    QJsonArray formatsArray = json["availableFormats"].toArray();
    for (const QJsonValue& value : formatsArray) {
        availableFormats.append(value.toString());
    }
    if (availableFormats.isEmpty()) {
        availableFormats = QStringList{"CSV", "JSON", "XML", "Excel"};
    }
    
    exportPath = json["exportPath"].toString("./exports");
    includeHeader = json["includeHeader"].toBool(true);
    dateTimeFormat = json["dateTimeFormat"].toString("yyyy-MM-dd hh:mm:ss.zzz");
    csvSeparator = json["csvSeparator"].toString(",");
    compressFiles = json["compressFiles"].toBool(false);
}

bool ExportConfig::validate() const {
    if (defaultFormat.isEmpty()) return false;
    if (availableFormats.isEmpty()) return false;
    if (!availableFormats.contains(defaultFormat)) return false;
    if (exportPath.isEmpty()) return false;
    if (dateTimeFormat.isEmpty()) return false;
    if (csvSeparator.isEmpty()) return false;
    return true;
}

void ExportConfig::setDefaults() {
    defaultFormat = "CSV";
    availableFormats = QStringList{"CSV", "JSON", "XML", "Excel"};
    exportPath = "./exports";
    includeHeader = true;
    dateTimeFormat = "yyyy-MM-dd hh:mm:ss.zzz";
    csvSeparator = ",";
    compressFiles = false;
}

//==============================================================================
// ConfigurationManager Implementation
//==============================================================================
ConfigurationManager::ConfigurationManager(QObject *parent)
    : QObject(parent)
{
    initializeDefaults();
    m_configPath = getDefaultConfigPath();
}

ConfigurationManager::~ConfigurationManager() {
    if (m_isDirty) {
        saveToFile();
    }
}

ConfigurationManager* ConfigurationManager::instance() {
    if (!s_instance) {
        s_instance = new ConfigurationManager();
    }
    return s_instance;
}

bool ConfigurationManager::loadFromFile(const QString& configPath) {
    QString filePath = configPath.isEmpty() ? m_configPath : configPath;
    
    QFile file(filePath);
    if (!file.exists()) {
        qDebug() << "Config file does not exist, creating with defaults:" << filePath;
        initializeDefaults();
        return saveToFile(filePath);
    }
    
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Cannot open config file for reading:" << filePath;
        return false;
    }
    
    QByteArray data = file.readAll();
    file.close();
    
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(data, &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error:" << parseError.errorString();
        emit validationError(QString("Config file JSON parse error: %1").arg(parseError.errorString()));
        return false;
    }
    
    QJsonObject rootObj = doc.object();
    
    // Check version and perform upgrade
    m_configVersion = rootObj["version"].toInt(1);
    if (m_configVersion < CURRENT_CONFIG_VERSION) {
        if (!upgradeConfig()) {
            qWarning() << "Failed to upgrade config from version" << m_configVersion;
            return false;
        }
    }
    
    // Load each config section
    try {
        m_serialPortConfig.fromJson(rootObj["serialPort"].toObject());
        m_dataAcquisitionConfig.fromJson(rootObj["dataAcquisition"].toObject());
        m_chartDisplayConfig.fromJson(rootObj["chartDisplay"].toObject());
        m_uiLayoutConfig.fromJson(rootObj["uiLayout"].toObject());
        m_exportConfig.fromJson(rootObj["export"].toObject());
        
        // Validate config
        if (!validateAllConfigs()) {
            qWarning() << "Config validation failed after loading";
            emit validationError("Config validation failed, using defaults");
            initializeDefaults();
            return false;
        }
        
        m_isDirty = false;
        emit configLoaded();
        qDebug() << "Configuration loaded successfully from:" << filePath;
        return true;
        
    } catch (const std::exception& e) {
        qWarning() << "Exception while loading config:" << e.what();
        emit validationError(QString("Exception occurred while loading config: %1").arg(e.what()));
        return false;
    }
}

bool ConfigurationManager::saveToFile(const QString& configPath) const {
    QString filePath = configPath.isEmpty() ? m_configPath : configPath;
    
    // Ensure directory exists
    QDir dir = QFileInfo(filePath).absoluteDir();
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            qWarning() << "Cannot create config directory:" << dir.absolutePath();
            return false;
        }
    }
    
    QJsonObject rootObj;
    rootObj["version"] = CURRENT_CONFIG_VERSION;
    rootObj["appName"] = QCoreApplication::applicationName();
    rootObj["appVersion"] = QCoreApplication::applicationVersion();
    rootObj["saveTime"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    rootObj["serialPort"] = m_serialPortConfig.toJson();
    rootObj["dataAcquisition"] = m_dataAcquisitionConfig.toJson();
    rootObj["chartDisplay"] = m_chartDisplayConfig.toJson();
    rootObj["uiLayout"] = m_uiLayoutConfig.toJson();
    rootObj["export"] = m_exportConfig.toJson();
    
    QJsonDocument doc(rootObj);
    
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Cannot open config file for writing:" << filePath;
        return false;
    }
    
    file.write(doc.toJson());
    file.close();
    
    const_cast<ConfigurationManager*>(this)->m_isDirty = false;
    const_cast<ConfigurationManager*>(this)->emit configSaved();
    qDebug() << "Configuration saved successfully to:" << filePath;
    return true;
}

bool ConfigurationManager::validateAllConfigs() const {
    if (!m_serialPortConfig.validate()) {
        qWarning() << "Serial port config validation failed";
        return false;
    }
    if (!m_dataAcquisitionConfig.validate()) {
        qWarning() << "Data acquisition config validation failed";
        return false;
    }
    if (!m_chartDisplayConfig.validate()) {
        qWarning() << "Chart display config validation failed";
        return false;
    }
    if (!m_uiLayoutConfig.validate()) {
        qWarning() << "UI layout config validation failed";
        return false;
    }
    if (!m_exportConfig.validate()) {
        qWarning() << "Export config validation failed";
        return false;
    }
    return true;
}

void ConfigurationManager::resetToDefaults() {
    m_serialPortConfig.setDefaults();
    m_dataAcquisitionConfig.setDefaults();
    m_chartDisplayConfig.setDefaults();
    m_uiLayoutConfig.setDefaults();
    m_exportConfig.setDefaults();
    
    m_configVersion = CURRENT_CONFIG_VERSION;
    m_isDirty = true;
    
    emit configChanged("all");
    qDebug() << "Configuration reset to defaults";
}

void ConfigurationManager::setConfigPath(const QString& path) {
    m_configPath = path;
}

QString ConfigurationManager::getConfigPath() const {
    return m_configPath;
}

bool ConfigurationManager::isUpgradeNeeded() const {
    return m_configVersion < CURRENT_CONFIG_VERSION;
}

bool ConfigurationManager::upgradeConfig() {
    if (m_configVersion >= CURRENT_CONFIG_VERSION) {
        return true; // No upgrade needed
    }
    
    qDebug() << "Upgrading config from version" << m_configVersion << "to" << CURRENT_CONFIG_VERSION;
    
    // TODO: Implement config upgrade logic
    // Currently only version 1, so directly set to current version
    m_configVersion = CURRENT_CONFIG_VERSION;
    m_isDirty = true;
    
    return true;
}

void ConfigurationManager::initializeDefaults() {
    m_serialPortConfig.setDefaults();
    m_dataAcquisitionConfig.setDefaults();
    m_chartDisplayConfig.setDefaults();
    m_uiLayoutConfig.setDefaults();
    m_exportConfig.setDefaults();
    
    m_configVersion = CURRENT_CONFIG_VERSION;
    m_isDirty = false;
}

bool ConfigurationManager::migrateFromLegacySettings() {
    // Try to read old config from QSettings
    QSettings settings;
    bool hasMigrated = false;
    
    // Migrate serial port config
    if (settings.contains("SerialPort/PortName")) {
        m_serialPortConfig.portName = settings.value("SerialPort/PortName").toString();
        m_serialPortConfig.baudRate = settings.value("SerialPort/BaudRate", 115200).toInt();
        hasMigrated = true;
    }
    
    // Migrate sampling config
    if (settings.contains("Sampling/Interval")) {
        m_dataAcquisitionConfig.samplingIntervalMs = settings.value("Sampling/Interval", 1000).toInt();
        hasMigrated = true;
    }
    
    // If migration successful, save new config and clean old settings
    if (hasMigrated) {
        m_isDirty = true;
        saveToFile();
        settings.clear(); // Clean old config
        qDebug() << "Successfully migrated legacy settings";
    }
    
    return hasMigrated;
}

QString ConfigurationManager::getDefaultConfigPath() const {
    QString configDir = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QDir().mkpath(configDir);
    return configDir + "/tempdetect_config.json";
} 