{"AUTOGEN_COMMAND_LINE_LENGTH_MAX": 32000, "BUILD_DIR": "C:/Users/<USER>/Desktop/TempDetectQt/build/TempDetectQt_autogen", "CMAKE_BINARY_DIR": "C:/Users/<USER>/Desktop/TempDetectQt/build", "CMAKE_CURRENT_BINARY_DIR": "C:/Users/<USER>/Desktop/TempDetectQt/build", "CMAKE_CURRENT_SOURCE_DIR": "C:/Users/<USER>/Desktop/TempDetectQt", "CMAKE_EXECUTABLE": "C:/Program Files/CMake/bin/cmake.exe", "CMAKE_LIST_FILES": ["C:/Users/<USER>/Desktop/TempDetectQt/CMakeLists.txt", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeSystem.cmake.in", "C:/Users/<USER>/Desktop/TempDetectQt/build/CMakeFiles/4.0.3/CMakeSystem.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Platform/Windows-Initialize.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCompilerIdDetection.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/ADSP-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/ARMCC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/ARMClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/AppleClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompilerInternal.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Borland-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Bruce-C-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompilerInternal.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Compaq-C-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Cray-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/CrayClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Embarcadero-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Fujitsu-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/GHS-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/GNU-C-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/HP-C-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/IAR-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Intel-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/LCC-C-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/MSVC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/NVHPC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/NVIDIA-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/OrangeC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/PGI-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/PathScale-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/SCO-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/SDCC-C-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/SunPro-C-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/TI-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/TIClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Tasking-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Watcom-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/XL-C-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/XLClang-C-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/zOS-C-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CompilerId/VS-10.vcxproj.in", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCCompiler.cmake.in", "C:/Users/<USER>/Desktop/TempDetectQt/build/CMakeFiles/4.0.3/CMakeCCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Platform/Windows-Determine-CXX.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCompilerIdDetection.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/ADSP-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/ARMCC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/ARMClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/AppleClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompilerInternal.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Borland-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompilerInternal.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Cray-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/CrayClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Embarcadero-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Fujitsu-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/GHS-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/HP-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/IAR-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Intel-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/MSVC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/NVHPC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/NVIDIA-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/OrangeC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/PGI-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/PathScale-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/SCO-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/TI-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/TIClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Tasking-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/Watcom-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/XL-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CompilerId/VS-10.vcxproj.in", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCXXCompiler.cmake.in", "C:/Users/<USER>/Desktop/TempDetectQt/build/CMakeFiles/4.0.3/CMakeCXXCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeGenericSystem.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Platform/Windows.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Platform/WindowsPaths.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCInformation.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/MSVC-C.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/MSVC.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Platform/Windows-MSVC-C.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Platform/Windows-MSVC.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineRCCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeRCCompiler.cmake.in", "C:/Users/<USER>/Desktop/TempDetectQt/build/CMakeFiles/4.0.3/CMakeRCCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeRCInformation.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestRCCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCompilerCommon.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeParseImplicitIncludeInfo.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeParseImplicitLinkInfo.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeParseLibraryArchitecture.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCompilerCommon.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCCompilerABI.c", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerSupport.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/FeatureTesting.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCCompiler.cmake.in", "C:/Users/<USER>/Desktop/TempDetectQt/build/CMakeFiles/4.0.3/CMakeCCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeCLinkerInformation.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Linker/MSVC-C.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Linker/MSVC.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC-C.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeInspectCLinker.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCCompiler.cmake.in", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCXXInformation.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/MSVC-CXX.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/MSVC.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Platform/Windows-MSVC-CXX.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Platform/Windows-MSVC.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCompilerCommon.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeParseImplicitIncludeInfo.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeParseImplicitLinkInfo.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeParseLibraryArchitecture.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCompilerCommon.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCXXCompilerABI.cpp", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerSupport.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/FeatureTesting.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCXXCompiler.cmake.in", "C:/Users/<USER>/Desktop/TempDetectQt/build/CMakeFiles/4.0.3/CMakeCXXCompiler.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeCXXLinkerInformation.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Linker/MSVC-CXX.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Linker/MSVC.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC-CXX.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeInspectCXXLinker.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeCXXCompiler.cmake.in", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5/Qt5Config.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeParseArguments.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QVirtualKeyboardPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsDirect2DIntegrationPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QWindowsIntegrationPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Widgets/Qt5Widgets_QWindowsVistaStylePlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Widgets/Qt5WidgetsConfigExtras.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5Widgets/Qt5WidgetsMacros.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeParseArguments.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfigVersion.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupportConfig.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5PrintSupport/Qt5PrintSupport_QWindowsPrinterSupportPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5OpenGL/Qt5OpenGLConfigVersion.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5OpenGL/Qt5OpenGLConfig.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfigVersion.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5SerialPort/Qt5SerialPortConfig.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5SerialBus/Qt5SerialBusConfigVersion.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5SerialBus/Qt5SerialBusConfig.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_PassThruCanBusPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_PeakCanBusPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_SystecCanBusPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_TinyCanBusPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_VectorCanBusPlugin.cmake", "C:/Qt/5.15.0/mingw81_64/lib/cmake/Qt5SerialBus/Qt5SerialBus_VirtualCanBusPlugin.cmake"], "CMAKE_SOURCE_DIR": "C:/Users/<USER>/Desktop/TempDetectQt", "CROSS_CONFIG": false, "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["C:/Users/<USER>/Desktop/TempDetectQt/configurationmanager.h", "MU", "EWIEGA46WW/moc_configurationmanager.cpp", null], ["C:/Users/<USER>/Desktop/TempDetectQt/mainwindow.h", "MU", "EWIEGA46WW/moc_mainwindow.cpp", null], ["C:/Users/<USER>/Desktop/TempDetectQt/modbusmanager.h", "MU", "EWIEGA46WW/moc_modbusmanager.cpp", null], ["C:/Users/<USER>/Desktop/TempDetectQt/qcustomplot.h", "MU", "EWIEGA46WW/moc_qcustomplot.cpp", null], ["C:/Users/<USER>/Desktop/TempDetectQt/temperaturedata.h", "MU", "EWIEGA46WW/moc_temperaturedata.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "C:/Users/<USER>/Desktop/TempDetectQt/build/TempDetectQt_autogen/include", "INCLUDE_DIR_Debug": "C:/Users/<USER>/Desktop/TempDetectQt/build/TempDetectQt_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "C:/Users/<USER>/Desktop/TempDetectQt/build/TempDetectQt_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "C:/Users/<USER>/Desktop/TempDetectQt/build/TempDetectQt_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "C:/Users/<USER>/Desktop/TempDetectQt/build/TempDetectQt_autogen/include_Release", "MOC_COMPILATION_FILE": "C:/Users/<USER>/Desktop/TempDetectQt/build/TempDetectQt_autogen/mocs_compilation.cpp", "MOC_COMPILATION_FILE_Debug": "C:/Users/<USER>/Desktop/TempDetectQt/build/TempDetectQt_autogen/mocs_compilation_Debug.cpp", "MOC_COMPILATION_FILE_MinSizeRel": "C:/Users/<USER>/Desktop/TempDetectQt/build/TempDetectQt_autogen/mocs_compilation_MinSizeRel.cpp", "MOC_COMPILATION_FILE_RelWithDebInfo": "C:/Users/<USER>/Desktop/TempDetectQt/build/TempDetectQt_autogen/mocs_compilation_RelWithDebInfo.cpp", "MOC_COMPILATION_FILE_Release": "C:/Users/<USER>/Desktop/TempDetectQt/build/TempDetectQt_autogen/mocs_compilation_Release.cpp", "MOC_DEFINITIONS": [], "MOC_DEFINITIONS_Debug": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_OPENGL_LIB", "QT_PRINTSUPPORT_LIB", "QT_SERIALBUS_LIB", "QT_SERIALPORT_LIB", "QT_WIDGETS_LIB", "WIN32"], "MOC_DEFINITIONS_MinSizeRel": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_OPENGL_LIB", "QT_PRINTSUPPORT_LIB", "QT_SERIALBUS_LIB", "QT_SERIALPORT_LIB", "QT_WIDGETS_LIB", "WIN32"], "MOC_DEFINITIONS_RelWithDebInfo": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_OPENGL_LIB", "QT_PRINTSUPPORT_LIB", "QT_SERIALBUS_LIB", "QT_SERIALPORT_LIB", "QT_WIDGETS_LIB", "WIN32"], "MOC_DEFINITIONS_Release": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_OPENGL_LIB", "QT_PRINTSUPPORT_LIB", "QT_SERIALBUS_LIB", "QT_SERIALPORT_LIB", "QT_WIDGETS_LIB", "WIN32"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": [], "MOC_INCLUDES_Debug": ["C:/Users/<USER>/Desktop/TempDetectQt", "/opt/homebrew/Cellar/qt@5/5.15.16/include", "/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialBus", "/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialPort", "C:/Qt/5.15.0/mingw81_64/include", "C:/Qt/5.15.0/mingw81_64/include/QtCore", "C:/Qt/5.15.0/mingw81_64/mkspecs/win32-g++", "C:/Qt/5.15.0/mingw81_64/include/QtGui", "C:/Qt/5.15.0/mingw81_64/include/QtANGLE", "C:/Qt/5.15.0/mingw81_64/include/QtWidgets", "C:/Qt/5.15.0/mingw81_64/include/QtPrintSupport", "C:/Qt/5.15.0/mingw81_64/include/QtOpenGL", "C:/Qt/5.15.0/mingw81_64/include/QtSerialPort", "C:/Qt/5.15.0/mingw81_64/include/QtSerialBus"], "MOC_INCLUDES_MinSizeRel": ["C:/Users/<USER>/Desktop/TempDetectQt", "/opt/homebrew/Cellar/qt@5/5.15.16/include", "/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialBus", "/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialPort", "C:/Qt/5.15.0/mingw81_64/include", "C:/Qt/5.15.0/mingw81_64/include/QtCore", "C:/Qt/5.15.0/mingw81_64/mkspecs/win32-g++", "C:/Qt/5.15.0/mingw81_64/include/QtGui", "C:/Qt/5.15.0/mingw81_64/include/QtANGLE", "C:/Qt/5.15.0/mingw81_64/include/QtWidgets", "C:/Qt/5.15.0/mingw81_64/include/QtPrintSupport", "C:/Qt/5.15.0/mingw81_64/include/QtOpenGL", "C:/Qt/5.15.0/mingw81_64/include/QtSerialPort", "C:/Qt/5.15.0/mingw81_64/include/QtSerialBus"], "MOC_INCLUDES_RelWithDebInfo": ["C:/Users/<USER>/Desktop/TempDetectQt", "/opt/homebrew/Cellar/qt@5/5.15.16/include", "/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialBus", "/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialPort", "C:/Qt/5.15.0/mingw81_64/include", "C:/Qt/5.15.0/mingw81_64/include/QtCore", "C:/Qt/5.15.0/mingw81_64/mkspecs/win32-g++", "C:/Qt/5.15.0/mingw81_64/include/QtGui", "C:/Qt/5.15.0/mingw81_64/include/QtANGLE", "C:/Qt/5.15.0/mingw81_64/include/QtWidgets", "C:/Qt/5.15.0/mingw81_64/include/QtPrintSupport", "C:/Qt/5.15.0/mingw81_64/include/QtOpenGL", "C:/Qt/5.15.0/mingw81_64/include/QtSerialPort", "C:/Qt/5.15.0/mingw81_64/include/QtSerialBus"], "MOC_INCLUDES_Release": ["C:/Users/<USER>/Desktop/TempDetectQt", "/opt/homebrew/Cellar/qt@5/5.15.16/include", "/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialBus", "/opt/homebrew/Cellar/qt@5/5.15.16/include/QtSerialPort", "C:/Qt/5.15.0/mingw81_64/include", "C:/Qt/5.15.0/mingw81_64/include/QtCore", "C:/Qt/5.15.0/mingw81_64/mkspecs/win32-g++", "C:/Qt/5.15.0/mingw81_64/include/QtGui", "C:/Qt/5.15.0/mingw81_64/include/QtANGLE", "C:/Qt/5.15.0/mingw81_64/include/QtWidgets", "C:/Qt/5.15.0/mingw81_64/include/QtPrintSupport", "C:/Qt/5.15.0/mingw81_64/include/QtOpenGL", "C:/Qt/5.15.0/mingw81_64/include/QtSerialPort", "C:/Qt/5.15.0/mingw81_64/include/QtSerialBus"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": [], "MOC_PREDEFS_FILE": "", "MOC_RELAXED_MODE": false, "MOC_SKIP": ["C:/Users/<USER>/Desktop/TempDetectQt/build/ui_mainwindow.h"], "MULTI_CONFIG": true, "PARALLEL": 24, "PARSE_CACHE_FILE": "C:/Users/<USER>/Desktop/TempDetectQt/build/CMakeFiles/TempDetectQt_autogen.dir/ParseCache.txt", "PARSE_CACHE_FILE_Debug": "C:/Users/<USER>/Desktop/TempDetectQt/build/CMakeFiles/TempDetectQt_autogen.dir/ParseCache_Debug.txt", "PARSE_CACHE_FILE_MinSizeRel": "C:/Users/<USER>/Desktop/TempDetectQt/build/CMakeFiles/TempDetectQt_autogen.dir/ParseCache_MinSizeRel.txt", "PARSE_CACHE_FILE_RelWithDebInfo": "C:/Users/<USER>/Desktop/TempDetectQt/build/CMakeFiles/TempDetectQt_autogen.dir/ParseCache_RelWithDebInfo.txt", "PARSE_CACHE_FILE_Release": "C:/Users/<USER>/Desktop/TempDetectQt/build/CMakeFiles/TempDetectQt_autogen.dir/ParseCache_Release.txt", "QT_MOC_EXECUTABLE": "C:/Qt/5.15.0/mingw81_64/bin/moc.exe", "QT_UIC_EXECUTABLE": "C:/Qt/5.15.0/mingw81_64/bin/uic.exe", "QT_VERSION_MAJOR": 5, "QT_VERSION_MINOR": 15, "SETTINGS_FILE": "C:/Users/<USER>/Desktop/TempDetectQt/build/CMakeFiles/TempDetectQt_autogen.dir/AutogenUsed.txt", "SETTINGS_FILE_Debug": "C:/Users/<USER>/Desktop/TempDetectQt/build/CMakeFiles/TempDetectQt_autogen.dir/AutogenUsed_Debug.txt", "SETTINGS_FILE_MinSizeRel": "C:/Users/<USER>/Desktop/TempDetectQt/build/CMakeFiles/TempDetectQt_autogen.dir/AutogenUsed_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "C:/Users/<USER>/Desktop/TempDetectQt/build/CMakeFiles/TempDetectQt_autogen.dir/AutogenUsed_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "C:/Users/<USER>/Desktop/TempDetectQt/build/CMakeFiles/TempDetectQt_autogen.dir/AutogenUsed_Release.txt", "SOURCES": [["C:/Users/<USER>/Desktop/TempDetectQt/configurationmanager.cpp", "MU", null], ["C:/Users/<USER>/Desktop/TempDetectQt/main.cpp", "MU", null], ["C:/Users/<USER>/Desktop/TempDetectQt/mainwindow.cpp", "MU", null], ["C:/Users/<USER>/Desktop/TempDetectQt/modbusmanager.cpp", "MU", null], ["C:/Users/<USER>/Desktop/TempDetectQt/qcustomplot.cpp", "MU", null], ["C:/Users/<USER>/Desktop/TempDetectQt/temperaturedata.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": ["C:/Users/<USER>/Desktop/TempDetectQt/build/ui_mainwindow.h", "C:/Users/<USER>/Desktop/TempDetectQt/mainwindow.ui"], "UIC_UI_FILES": [], "USE_BETTER_GRAPH": false, "VERBOSITY": 0}